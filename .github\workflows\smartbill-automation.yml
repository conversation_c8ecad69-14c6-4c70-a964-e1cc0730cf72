name: <PERSON><PERSON>ill Address Correction Automation

on:
  # Run nightly at midnight Europe/Bucharest time (22:00 UTC in winter, 21:00 UTC in summer)
  schedule:
    - cron: '0 22 * * *'  # Adjust for daylight saving time as needed
  
  # Allow manual triggering for testing
  workflow_dispatch:
    inputs:
      headless:
        description: 'Run browser in headless mode'
        required: false
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'

jobs:
  address-correction:
    runs-on: ubuntu-latest
    timeout-minutes: 60  # Prevent jobs from running too long
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Install Playwright browsers
      run: |
        playwright install chromium
        playwright install-deps chromium
    
    - name: Create Google Sheets credentials file
      run: |
        echo '${{ secrets.GOOGLE_SHEETS_CREDENTIALS }}' > credentials.json
    
    - name: Run SmartBill automation
      env:
        # SmartBill credentials
        SMARTBILL_USERNAME: ${{ secrets.SMARTBILL_USERNAME }}
        SMARTBILL_PASSWORD: ${{ secrets.SMARTBILL_PASSWORD }}
        TOTP_SECRET: ${{ secrets.TOTP_SECRET }}
        
        # Google APIs
        GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
        GOOGLE_SHEETS_CREDENTIALS_PATH: credentials.json
        GOOGLE_SHEETS_ID: ${{ secrets.GOOGLE_SHEETS_ID }}
        
        # Telegram notifications
        TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
        TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        
        # Browser settings
        HEADLESS: ${{ github.event.inputs.headless || 'true' }}
        BROWSER_TIMEOUT: 30000
        
        # Retry settings
        MAX_RETRIES: 5
        RETRY_DELAY: 1.0
        
        # Timezone
        TIMEZONE: Europe/Bucharest
      run: |
        python -m src.main
    
    - name: Upload logs on failure
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: automation-logs
        path: |
          smartbill_automation.log
          *.log
        retention-days: 30
    
    - name: Cleanup credentials
      if: always()
      run: |
        rm -f credentials.json
