"""Google Maps geocoding service for address standardization."""

import logging
import re
import time
from typing import Dict, Op<PERSON>, Tuple
import unicodedata

import googlemaps
from googlemaps.exceptions import ApiError, Timeout, TransportError

from .config import Config

logger = logging.getLogger(__name__)


class GeocodingService:
    """Handles address geocoding and standardization using Google Maps API."""
    
    def __init__(self, config: Config):
        self.config = config
        self.client = googlemaps.Client(key=config.google_maps_api_key)
        self.cache = {}  # Simple in-memory cache for the session
    
    def normalize_text(self, text: str) -> str:
        """
        Normalize text by removing diacritics and extra whitespace.
        
        Args:
            text: Input text to normalize
            
        Returns:
            str: Normalized text
        """
        if not text:
            return ""
        
        # Remove diacritics
        normalized = unicodedata.normalize('NFD', text)
        ascii_text = normalized.encode('ascii', 'ignore').decode('ascii')
        
        # Clean up whitespace and punctuation
        cleaned = re.sub(r'\s+', ' ', ascii_text.strip())
        
        return cleaned
    
    def build_search_query(self, address: str, city: str, county: str) -> str:
        """
        Build a search query for Google Maps geocoding.
        
        Args:
            address: Street address
            city: City name
            county: County name
            
        Returns:
            str: Formatted search query
        """
        # Normalize inputs
        address = self.normalize_text(address)
        city = self.normalize_text(city)
        county = self.normalize_text(county)
        
        # Build query components
        query_parts = []
        
        if address:
            query_parts.append(address)
        
        if city:
            query_parts.append(city)
        
        if county:
            # Add county with "Judet" suffix if not present
            if not county.lower().endswith(('judet', 'county')):
                county += " Judet"
            query_parts.append(county)
        
        # Always add Romania to ensure we get Romanian addresses
        query_parts.append("Romania")
        
        return ", ".join(query_parts)
    
    def extract_address_components(self, geocoding_result: Dict) -> Tuple[str, str, str]:
        """
        Extract standardized address components from Google Maps result.
        For Bucharest addresses, extracts the sector information.

        Args:
            geocoding_result: Google Maps geocoding result

        Returns:
            Tuple[str, str, str]: (address, city/sector, county)
        """
        if not geocoding_result or 'address_components' not in geocoding_result:
            logger.warning("No address components in geocoding result")
            return "", "", ""

        components = geocoding_result['address_components']
        logger.info(f"=== EXTRACTING FROM {len(components)} ADDRESS COMPONENTS ===")

        # Initialize components
        street_number = ""
        route = ""
        locality = ""
        sublocality = ""
        administrative_area_level_1 = ""  # County
        administrative_area_level_2 = ""  # Sometimes used for sectors

        # Extract components with detailed logging
        for i, component in enumerate(components):
            types = component.get('types', [])
            long_name = component.get('long_name', '')
            short_name = component.get('short_name', '')

            logger.info(f"Component {i+1}: '{long_name}' (short: '{short_name}') - types: {types}")

            if 'street_number' in types:
                street_number = long_name
                logger.info(f"  → Street number: '{street_number}'")
            elif 'route' in types:
                route = long_name
                logger.info(f"  → Route: '{route}'")
            elif 'locality' in types:
                locality = long_name
                logger.info(f"  → Locality: '{locality}'")
            elif 'sublocality' in types or 'sublocality_level_1' in types:
                sublocality = long_name
                logger.info(f"  → Sublocality: '{sublocality}'")
            elif 'administrative_area_level_1' in types:
                administrative_area_level_1 = long_name
                logger.info(f"  → Admin level 1 (County): '{administrative_area_level_1}'")
            elif 'administrative_area_level_2' in types:
                administrative_area_level_2 = long_name
                logger.info(f"  → Admin level 2: '{administrative_area_level_2}'")

        # Build standardized address
        address_parts = []
        if route:
            address_parts.append(route)
        if street_number:
            address_parts.append(street_number)

        standardized_address = " ".join(address_parts)

        # Clean county name (remove "Judet" suffix if present)
        county = administrative_area_level_1
        if county.lower().endswith(' judet'):
            county = county[:-6].strip()
        elif county.lower().endswith(' county'):
            county = county[:-7].strip()

        # Determine city/sector for Bucharest
        city = locality

        # Check if this is a Bucharest address
        logger.info(f"=== BUCHAREST SECTOR DETECTION ===")

        # Normalize Romanian characters for comparison
        county_normalized = self.normalize_text(county.lower()) if county else ""
        locality_normalized = self.normalize_text(locality.lower()) if locality else ""

        logger.info(f"County: '{county}' -> normalized: '{county_normalized}'")
        logger.info(f"Locality: '{locality}' -> normalized: '{locality_normalized}'")

        bucharest_variants = ['bucuresti', 'bucharest', 'bucureşti', 'bucuresti']
        county_is_bucharest = any(variant in county_normalized for variant in bucharest_variants)
        locality_is_bucharest = any(variant in locality_normalized for variant in bucharest_variants)

        logger.info(f"County is Bucharest: {county_is_bucharest}")
        logger.info(f"Locality is Bucharest: {locality_is_bucharest}")

        if county_is_bucharest or locality_is_bucharest:

            logger.info("✓ Confirmed Bucharest location - attempting sector extraction")

            # Try to extract sector from sublocality or administrative_area_level_2
            logger.info(f"Sector extraction inputs:")
            logger.info(f"  Sublocality: '{sublocality}'")
            logger.info(f"  Admin area level 2: '{administrative_area_level_2}'")

            sector = self.extract_bucharest_sector_from_components(
                sublocality, administrative_area_level_2, components
            )

            if sector:
                city = sector
                county = "Bucuresti"  # Standardize county name
                logger.info(f"✓ SUCCESS: Extracted Bucharest sector from components: '{sector}'")
            else:
                logger.info("⚠ No sector found in components - trying full address analysis")
                # Fallback: try to determine sector from the full address
                full_address = geocoding_result.get('formatted_address', '')
                logger.info(f"Full address for analysis: '{full_address}'")

                sector = self.determine_sector_from_full_address(full_address)
                if sector:
                    city = sector
                    county = "Bucuresti"
                    logger.info(f"SUCCESS: Determined Bucharest sector from full address: '{sector}'")
                else:
                    # Final fallback: Use reverse geocoding (as per documentation)
                    logger.info("Trying reverse geocoding as final fallback...")
                    location = geocoding_result.get('geometry', {}).get('location', {})
                    if location.get('lat') and location.get('lng'):
                        import asyncio
                        try:
                            # Note: This is a sync method, but we'll handle it
                            sector = asyncio.run(self.get_sector_via_reverse_geocoding(
                                location['lat'], location['lng']
                            ))
                            if sector:
                                city = sector
                                county = "Bucuresti"
                                logger.info(f"SUCCESS: Found sector via reverse geocoding: '{sector}'")
                            else:
                                city = "Bucuresti"
                                logger.warning("FAILED: Could not determine Bucharest sector from any method")
                                logger.warning("This address may not be in a numbered sector or Google Maps lacks sector detail")
                        except Exception as e:
                            logger.error(f"Reverse geocoding failed: {e}")
                            city = "Bucuresti"
                            logger.warning("FAILED: Could not determine Bucharest sector from any method")
                    else:
                        city = "Bucuresti"
                        logger.warning("FAILED: No coordinates available for reverse geocoding")
        else:
            logger.info("Not a Bucharest location - using locality as city")

        return standardized_address, city, county

    def extract_bucharest_sector_from_components(self, sublocality: str, admin_area_2: str, all_components: list) -> str:
        """
        Extract Bucharest sector from Google Maps address components using comprehensive strategies.

        Args:
            sublocality: Sublocality component
            admin_area_2: Administrative area level 2
            all_components: All address components for additional analysis

        Returns:
            str: Sector name (e.g., "Sector 1") or empty string
        """
        logger.info("=== SECTOR EXTRACTION FROM COMPONENTS ===")
        logger.info(f"Sublocality: '{sublocality}'")
        logger.info(f"Admin area level 2: '{admin_area_2}'")
        logger.info(f"Total components: {len(all_components)}")

        # Strategy 1: Check sublocality_level_1 first (most common for sectors per documentation)
        if sublocality:
            sector = self.parse_sector_from_text(sublocality)
            if sector:
                logger.info(f"Found sector in sublocality: '{sector}'")
                return sector

        # Strategy 2: Check administrative_area_level_2 (sectors are admin units)
        if admin_area_2:
            sector = self.parse_sector_from_text(admin_area_2)
            if sector:
                logger.info(f"Found sector in admin_area_2: '{sector}'")
                return sector

        # Strategy 3: Check all components for sector information (comprehensive search)
        for i, component in enumerate(all_components):
            long_name = component.get('long_name', '')
            types = component.get('types', [])

            logger.info(f"Component {i+1}: '{long_name}' - types: {types}")

            # Look for sector in components that might contain it (per documentation)
            if any(t in types for t in ['sublocality_level_1', 'administrative_area_level_2', 'political', 'sublocality', 'neighborhood']):
                sector = self.parse_sector_from_text(long_name)
                if sector:
                    logger.info(f"Found sector in component {i+1}: '{sector}'")
                    return sector

        # Strategy 4: Postal code method (Bucharest postal codes: 01xxxx = Sector 1, etc.)
        logger.info("=== POSTAL CODE SECTOR DETECTION ===")
        for component in all_components:
            if "postal_code" in component.get('types', []):
                postal_code = component.get('long_name', '')
                logger.info(f"Found postal code: '{postal_code}'")

                if len(postal_code) == 6 and postal_code[:2].isdigit():
                    sector_num = int(postal_code[:2])
                    if 1 <= sector_num <= 6:
                        sector = f"Sector {sector_num}"
                        logger.info(f"Derived sector from postal code: '{sector}'")
                        return sector
                    else:
                        logger.info(f"Postal code sector number {sector_num} is out of range (1-6)")
                else:
                    logger.info(f"Postal code format invalid: '{postal_code}'")

        logger.info("No sector found in any component")
        return ""

    async def get_sector_via_reverse_geocoding(self, lat: float, lng: float) -> str:
        """
        Use reverse geocoding to get sector information for Bucharest coordinates.
        This is a fallback method when forward geocoding doesn't return sector info.

        Args:
            lat: Latitude
            lng: Longitude

        Returns:
            str: Sector name (e.g., "Sector 1") or empty string
        """
        try:
            logger.info("=== REVERSE GEOCODING FOR SECTOR ===")
            logger.info(f"Coordinates: {lat}, {lng}")

            # Use reverse geocoding with administrative_area_level_2 filter
            # This should return the sector as per the documentation
            reverse_results = self.client.reverse_geocode(
                (lat, lng),
                result_type=["administrative_area_level_2"]
            )

            logger.info(f"Reverse geocoding returned {len(reverse_results)} results")

            if reverse_results:
                result = reverse_results[0]
                logger.info(f"Reverse geocoding formatted address: {result.get('formatted_address', '')}")

                # Check address components for sector
                components = result.get('address_components', [])
                for component in components:
                    types = component.get('types', [])
                    long_name = component.get('long_name', '')

                    if 'administrative_area_level_2' in types:
                        logger.info(f"Found admin_area_level_2: '{long_name}'")
                        sector = self.parse_sector_from_text(long_name)
                        if sector:
                            logger.info(f"Extracted sector from reverse geocoding: '{sector}'")
                            return sector

                # Also try sublocality_level_1 as fallback
                logger.info("Trying sublocality_level_1 as fallback...")
                reverse_results_sublocality = self.client.reverse_geocode(
                    (lat, lng),
                    result_type=["sublocality_level_1"]
                )

                if reverse_results_sublocality:
                    result = reverse_results_sublocality[0]
                    components = result.get('address_components', [])
                    for component in components:
                        types = component.get('types', [])
                        long_name = component.get('long_name', '')

                        if 'sublocality_level_1' in types:
                            logger.info(f"Found sublocality_level_1: '{long_name}'")
                            sector = self.parse_sector_from_text(long_name)
                            if sector:
                                logger.info(f"Extracted sector from reverse geocoding (sublocality): '{sector}'")
                                return sector

            logger.info("No sector found via reverse geocoding")
            return ""

        except Exception as e:
            logger.error(f"Reverse geocoding failed: {e}")
            return ""

    def parse_sector_from_text(self, text: str) -> str:
        """
        Parse sector number from text.

        Args:
            text: Text that might contain sector information

        Returns:
            str: Sector name (e.g., "Sector 1") or empty string
        """
        if not text:
            return ""

        text_lower = text.lower()

        # Common patterns for Bucharest sectors
        import re

        # Pattern 1: "Sector 1", "Sectorul 1", etc.
        sector_patterns = [
            r'sector(?:ul)?\s*(\d+)',
            r'sect(?:or)?\.?\s*(\d+)',
            r'(\d+)(?:st|nd|rd|th)?\s*sector',
        ]

        for pattern in sector_patterns:
            match = re.search(pattern, text_lower)
            if match:
                sector_num = match.group(1)
                if sector_num in ['1', '2', '3', '4', '5', '6']:
                    return f"Sector {sector_num}"

        # Pattern 2: Direct sector names in Romanian
        sector_names = {
            'sectorul 1': 'Sector 1',
            'sectorul 2': 'Sector 2',
            'sectorul 3': 'Sector 3',
            'sectorul 4': 'Sector 4',
            'sectorul 5': 'Sector 5',
            'sectorul 6': 'Sector 6',
            'sector 1': 'Sector 1',
            'sector 2': 'Sector 2',
            'sector 3': 'Sector 3',
            'sector 4': 'Sector 4',
            'sector 5': 'Sector 5',
            'sector 6': 'Sector 6'
        }

        for key, value in sector_names.items():
            if key in text_lower:
                return value

        return ""

    def determine_sector_from_full_address(self, formatted_address: str) -> str:
        """
        Determine sector from the full formatted address returned by Google Maps.

        Args:
            formatted_address: Full formatted address from Google Maps

        Returns:
            str: Sector name (e.g., "Sector 1") or empty string
        """
        if not formatted_address:
            return ""

        logger.debug(f"Analyzing full address for sector: {formatted_address}")

        # Try to parse sector from the full address
        sector = self.parse_sector_from_text(formatted_address)
        if sector:
            return sector

        # If no explicit sector found, try to determine from known landmarks/areas
        address_lower = formatted_address.lower()

        # Known area-to-sector mappings based on major landmarks
        area_sector_map = {
            # Sector 1 landmarks
            'piața română': 'Sector 1',
            'piata romana': 'Sector 1',
            'calea victoriei': 'Sector 1',
            'herastrau': 'Sector 1',
            'arcul de triumf': 'Sector 1',
            'aviatorilor': 'Sector 1',

            # Sector 2 landmarks
            'piața obor': 'Sector 2',
            'piata obor': 'Sector 2',
            'calea moșilor': 'Sector 2',
            'calea mosilor': 'Sector 2',
            'ștefan cel mare': 'Sector 2',
            'stefan cel mare': 'Sector 2',

            # Sector 3 landmarks
            'piața unirii': 'Sector 3',
            'piata unirii': 'Sector 3',
            'bulevardul unirii': 'Sector 3',
            'calea vitan': 'Sector 3',
            'dudești': 'Sector 3',
            'dudesti': 'Sector 3',

            # Sector 4 landmarks
            'piața sudului': 'Sector 4',
            'piata sudului': 'Sector 4',
            'berceni': 'Sector 4',
            'oltenitei': 'Sector 4',
            'tineretului': 'Sector 4',

            # Sector 5 landmarks
            'drumul taberei': 'Sector 5',
            'cotroceni': 'Sector 5',
            'râul doamnei': 'Sector 5',
            'raul doamnei': 'Sector 5',
            '13 septembrie': 'Sector 5',

            # Sector 6 landmarks
            'giulești': 'Sector 6',
            'giulesti': 'Sector 6',
            'militari': 'Sector 6',
            'crângași': 'Sector 6',
            'crangasi': 'Sector 6'
        }

        for landmark, sector in area_sector_map.items():
            if landmark in address_lower:
                logger.info(f"Determined sector {sector} from landmark: {landmark}")
                return sector

        return ""
    
    async def geocode_address(self, address: str, city: str, county: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Geocode and standardize an address using Google Maps API.
        
        Args:
            address: Current address
            city: Current city
            county: Current county
            
        Returns:
            Tuple[Optional[str], Optional[str], Optional[str]]: (new_address, new_city, new_county)
        """
        try:
            # Create cache key
            cache_key = f"{address}|{city}|{county}"
            
            # Check cache first
            if cache_key in self.cache:
                logger.debug(f"Using cached result for: {cache_key}")
                return self.cache[cache_key]
            
            # Build search query
            search_query = self.build_search_query(address, city, county)
            logger.info(f"Geocoding query: {search_query}")
            
            # Perform geocoding
            results = self.client.geocode(
                search_query,
                region='ro',  # Bias towards Romania
                language='ro'  # Romanian language
            )
            
            if not results:
                logger.warning(f"No geocoding results for: {search_query}")
                self.cache[cache_key] = (None, None, None)
                return None, None, None
            
            # Use the first (best) result
            best_result = results[0]
            
            # Extract standardized components
            new_address, new_city, new_county = self.extract_address_components(best_result)
            
            # Validate that we got meaningful results
            if not new_city and not new_county:
                logger.warning(f"Geocoding returned incomplete results for: {search_query}")
                self.cache[cache_key] = (None, None, None)
                return None, None, None
            
            # Cache the result
            result = (new_address or None, new_city or None, new_county or None)
            self.cache[cache_key] = result
            
            # Sanitize Romanian characters for logging to avoid Unicode errors
            safe_search_query = search_query.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
            safe_new_address = new_address.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
            safe_new_city = new_city.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
            safe_new_county = new_county.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
            logger.info(f"Geocoded '{safe_search_query}' -> Address: '{safe_new_address}', City: '{safe_new_city}', County: '{safe_new_county}'")
            
            return result
            
        except (ApiError, Timeout, TransportError) as e:
            logger.error(f"Google Maps API error for '{address}, {city}, {county}': {e}")
            return None, None, None
        except Exception as e:
            logger.error(f"Unexpected error during geocoding for '{address}, {city}, {county}': {e}")
            return None, None, None
    
    def clear_cache(self) -> None:
        """Clear the geocoding cache."""
        self.cache.clear()
        logger.info("Geocoding cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        return {
            "cache_size": len(self.cache),
            "cache_hits": sum(1 for v in self.cache.values() if v != (None, None, None)),
            "cache_misses": sum(1 for v in self.cache.values() if v == (None, None, None))
        }
