"""
SmartBill automation module for e-Invoice error correction.
This module handles browser automation for SmartBill Cloud platform.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple
import pyotp

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

from .config import Config
from .models import AddressRecord

logger = logging.getLogger(__name__)


class SmartBillAutomation:
    """Handles SmartBill browser automation for e-Invoice error correction."""

    def __init__(self, config: Config):
        self.config = config
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.totp = pyotp.TOTP(config.totp_secret)

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_browser()

    async def start_browser(self) -> None:
        """Initialize and start the browser."""
        playwright = await async_playwright().start()

        self.browser = await playwright.chromium.launch(
            headless=self.config.headless,
            args=[
                '--no-sandbox', 
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ]
        )

        self.context = await self.browser.new_context(
             # This makes it use the full window size
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        self.page = await self.context.new_page()
        self.page.set_default_timeout(self.config.browser_timeout)

        logger.info(f"Browser started successfully (headless: {self.config.headless})")

    async def close_browser(self) -> None:
        """Close the browser and cleanup."""
        try:
            if self.context:
                await self.context.close()
                logger.debug("Browser context closed")
        except Exception as e:
            logger.debug(f"Error closing context: {e}")

        try:
            if self.browser:
                await self.browser.close()
                logger.info("Browser closed")
        except Exception as e:
            logger.debug(f"Error closing browser: {e}")

        # Give time for cleanup
        await asyncio.sleep(1)

    async def handle_cookie_popup(self) -> None:
        """Handle cookie consent popup if it appears."""
        try:
            logger.info("Checking for cookie consent popup...")

            # Wait a bit for any popups to load
            await asyncio.sleep(2)

            # Common selectors for OneTrust and other cookie popups
            cookie_selectors = [
                '#onetrust-accept-btn-handler',  # Primary OneTrust selector
                'button:has-text("Accept toate cookie-urile")',  # Exact text match
                'button:has-text("Accept")',
                'button:has-text("Accept All")',
                'button:has-text("Acceptă")',
                'button:has-text("Acceptă toate")',
                '.onetrust-close-btn-handler',
                '[data-testid="accept-cookies"]',
                '.cookie-accept',
                '.accept-cookies',
                '#cookie-accept'
            ]

            for selector in cookie_selectors:
                try:
                    # Check if element exists and is visible
                    element = await self.page.query_selector(selector)
                    if element:
                        # Scroll into view first
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(0.3)

                        is_visible = await element.is_visible()
                        if is_visible:
                            logger.info(f"Found cookie button with selector: {selector}")
                            await element.click()
                            logger.info(f"Successfully clicked cookie accept button")
                            await asyncio.sleep(2)  # Wait for popup to close
                            return
                        else:
                            logger.debug(f"Cookie button found but not visible: {selector}")
                    else:
                        logger.debug(f"Cookie button not found: {selector}")
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue

            logger.info("No cookie popup detected or already handled")

        except Exception as e:
            logger.warning(f"Error handling cookie popup: {e}")

    async def login(self) -> bool:
        """
        Perform login to SmartBill with MFA support.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Starting SmartBill login process")

            # Navigate to SmartBill Cloud login page
            logger.info("Navigating to SmartBill login page...")
            await self.page.goto("https://cloud.smartbill.ro/auth/login/", wait_until='domcontentloaded')

            # Wait for page to load
            logger.info("Waiting for page to load...")
            try:
                await self.page.wait_for_load_state('domcontentloaded', timeout=60000)
                logger.info("DOM content loaded")
                await asyncio.sleep(3)  # Give time for any dynamic content
            except Exception as e:
                logger.warning(f"Page load timeout, continuing anyway: {e}")

            # Handle cookie popup if it appears
            await self.handle_cookie_popup()

            # Wait a bit more to ensure page is fully loaded
            await asyncio.sleep(2)

            # Fill username using the exact selector from the form
            try:
                await self.page.fill('#id_username', self.config.smartbill_username)
                logger.debug("Username filled successfully")
            except Exception as e:
                logger.error(f"Failed to fill username: {e}")
                raise Exception("Could not find username input field")

            # Fill password using the exact selector from the form
            try:
                await self.page.fill('#id_password', self.config.smartbill_password)
                logger.debug("Password filled successfully")
            except Exception as e:
                logger.error(f"Failed to fill password: {e}")
                raise Exception("Could not find password input field")

            # Click login button using the exact selector from the form
            try:
                await self.page.click('#cloud_login_btn')
                logger.debug("Login button clicked successfully")
            except Exception as e:
                logger.error(f"Failed to click login button: {e}")
                raise Exception("Could not find login button")

            # Wait for page response after login click
            logger.info("Waiting for page response after login...")
            await asyncio.sleep(3)  # Give time for the page to respond

            # Check if MFA is required (2FA field appears on same page)
            try:
                logger.info("Checking for MFA/2FA requirement...")

                # Look for TOTP/MFA input field with the exact selector
                totp_selectors = [
                    '#2fa_code',  # Exact selector from your HTML
                    'input[name="2fa_code"]',  # Alternative by name
                    'input[id="2fa_code"]',  # Alternative by id
                    'input[placeholder*="Codul de securitate"]',  # By placeholder text
                    'input[name="totp"]',
                    'input[name="code"]',
                    'input[name="verification_code"]',
                    'input[placeholder*="cod"]',
                    'input[placeholder*="code"]'
                ]

                totp_input = None
                for selector in totp_selectors:
                    try:
                        # Check if the element exists first
                        element = await self.page.query_selector(selector)
                        if element:
                            is_visible = await element.is_visible()
                            if is_visible:
                                totp_input = element
                                logger.info(f"Found MFA input field using selector: {selector}")
                                break
                            else:
                                logger.debug(f"MFA field found but not visible: {selector}")
                        else:
                            logger.debug(f"MFA field not found: {selector}")
                    except Exception as e:
                        logger.debug(f"Error checking selector {selector}: {e}")
                        continue

                # If not found immediately, wait a bit and try again
                if not totp_input:
                    logger.info("2FA field not found immediately, waiting and retrying...")
                    await asyncio.sleep(2)

                    for selector in totp_selectors:
                        try:
                            element = await self.page.query_selector(selector)
                            if element:
                                is_visible = await element.is_visible()
                                if is_visible:
                                    totp_input = element
                                    logger.info(f"Found MFA input field on retry using selector: {selector}")
                                    break
                        except Exception:
                            continue

                if totp_input:
                    logger.info("MFA required, generating TOTP code")

                    # Generate TOTP code
                    totp_code = self.totp.now()
                    logger.info(f"Generated TOTP code: {totp_code}")

                    # Validate TOTP code format (should be 6 digits)
                    if not totp_code or len(totp_code) != 6 or not totp_code.isdigit():
                        logger.error(f"Invalid TOTP code format: '{totp_code}' (should be 6 digits)")
                        # Try generating again
                        await asyncio.sleep(1)
                        totp_code = self.totp.now()
                        logger.info(f"Regenerated TOTP code: {totp_code}")

                        if not totp_code or len(totp_code) != 6 or not totp_code.isdigit():
                            logger.error(f"TOTP code still invalid: '{totp_code}'")
                            raise Exception("Cannot generate valid TOTP code")

                    # Focus on the input field first
                    await totp_input.focus()
                    await asyncio.sleep(0.5)

                    # Clear and fill the MFA input using element methods
                    try:
                        # Use the element handle directly since we already have it
                        # First clear by selecting all and deleting
                        await totp_input.click(click_count=3)  # Select all
                        await self.page.keyboard.press('Delete')  # Delete selected
                        await asyncio.sleep(0.3)

                        # Type the TOTP code character by character for better reliability
                        await self.page.keyboard.type(totp_code)
                        logger.info("TOTP code entered using keyboard.type()")

                    except Exception as e:
                        logger.warning(f"Keyboard method failed, trying alternative: {e}")
                        try:
                            # Alternative: use JavaScript to set the value
                            await self.page.evaluate(f'''
                                const input = document.querySelector('input[name="2fa_code"]') || document.getElementById('2fa_code');
                                if (input) {{
                                    input.value = '{totp_code}';
                                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                }}
                            ''')
                            logger.info("TOTP code entered using JavaScript")
                        except Exception as e2:
                            logger.error(f"All input methods failed: {e2}")

                    # Wait a moment for the input to register
                    await asyncio.sleep(1)

                    # Verify the code was entered using JavaScript
                    try:
                        entered_value = await self.page.evaluate('''
                            const input = document.querySelector('input[name="2fa_code"]') || document.getElementById('2fa_code');
                            return input ? input.value : '';
                        ''')
                        logger.info(f"Value in 2FA field: '{entered_value}'")

                        # Check if the entered value matches what we expect
                        if entered_value != totp_code:
                            logger.warning(f"Entered value '{entered_value}' doesn't match expected '{totp_code}'")

                    except Exception as e:
                        logger.warning(f"Could not verify entered value: {e}")

                    # Submit MFA code - look for the exact submit button
                    mfa_submit_selectors = [
                        '#confirm_2fa_code',  # Exact button from your HTML
                        'a:has-text("Continua")',  # By text
                        '.twofa-confirmation',  # By class
                        'a.btn.btn-primary:has-text("Continua")',  # More specific
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button:has-text("Continue")',
                        'button:has-text("Continuă")',
                        '#cloud_login_btn'  # Fallback
                    ]

                    submit_clicked = False
                    for selector in mfa_submit_selectors:
                        try:
                            submit_button = await self.page.query_selector(selector)
                            if submit_button:
                                is_visible = await submit_button.is_visible()
                                if is_visible:
                                    logger.info(f"Found submit button: {selector}")
                                    await submit_button.click()
                                    logger.info(f"Clicked MFA submit button using selector: {selector}")
                                    submit_clicked = True
                                    break
                                else:
                                    logger.debug(f"Submit button found but not visible: {selector}")
                            else:
                                logger.debug(f"Submit button not found: {selector}")
                        except Exception as e:
                            logger.debug(f"Error with submit selector {selector}: {e}")
                            continue

                    if not submit_clicked:
                        # Try pressing Enter as fallback
                        logger.info("No submit button found, trying Enter key on 2FA input")
                        await totp_input.press('Enter')

                    # Wait for page to process MFA
                    logger.info("Waiting for MFA processing...")
                    await asyncio.sleep(5)

                    try:
                        await self.page.wait_for_load_state('domcontentloaded', timeout=30000)
                    except Exception as e:
                        logger.warning(f"Page load timeout after MFA, continuing: {e}")
                else:
                    logger.info("No MFA input field found")

            except Exception as e:
                logger.warning(f"Error handling MFA: {e}")

            # After MFA (if required), wait for successful login
            logger.info("Checking for successful login...")

            # Wait for successful login - check for dashboard or main application
            success_indicators = [
                '.main-content',
                '.dashboard',
                '[data-testid="dashboard"]',
                '.navbar',
                '.sidebar',
                'a[href*="raport"]',
                'a[href*="facturi"]'
            ]

            login_successful = False

            # Wait a bit for redirect after MFA
            await asyncio.sleep(3)

            for indicator in success_indicators:
                try:
                    await self.page.wait_for_selector(indicator, timeout=10000)
                    login_successful = True
                    logger.info(f"Login success detected using indicator: {indicator}")
                    break
                except Exception:
                    continue

            if not login_successful:
                # Check if we're redirected to a dashboard URL
                current_url = self.page.url
                logger.info(f"Current URL after login: {current_url}")
                if 'cloud.smartbill.ro' in current_url and 'login' not in current_url:
                    login_successful = True
                    logger.info(f"Login success detected by URL change: {current_url}")

            if login_successful:
                logger.info("Login completed successfully")
                return True
            else:
                current_url = self.page.url
                logger.error(f"Login verification failed. Current URL: {current_url}")

                # Check if we're still on login page with 2FA
                if 'login' in current_url:
                    totp_field = await self.page.query_selector('#2fa_code')
                    if totp_field:
                        logger.error("Still on login page with 2FA field visible - MFA may have failed")
                    else:
                        logger.error("Still on login page but no 2FA field - check credentials")

                raise Exception("Could not verify successful login")

        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False

    async def navigate_to_einvoice_errors(self) -> bool:
        """
        Navigate to the e-Invoice errors section and apply filter.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            logger.info("Navigating to e-Invoice errors section")

            # Navigate directly to the invoices report page
            logger.info("Loading invoices page...")
            await self.page.goto("https://cloud.smartbill.ro/raport/facturi/", timeout=60000)  # Increased timeout

            # Wait for page to load with longer timeout for slow internet
            try:
                await self.page.wait_for_load_state('domcontentloaded', timeout=60000)
                logger.info("Invoices page loaded")
            except Exception as e:
                logger.warning(f"Page load timeout, continuing anyway: {e}")

            # Wait for the page to fully load
            await asyncio.sleep(3)

            # Go straight to dropdown filter (skip button attempt)
            logger.info("Applying e-Invoice error filter using dropdown...")

            try:
                # Click the dropdown to open it
                dropdown_click_selectors = [
                    '.einvoice-status-restricted .dropdown.easydropdown .selected',  # The "Toate" span
                    '.einvoice-status-restricted .dropdown.easydropdown',  # The dropdown container
                    '.dropdown.easydropdown .selected',  # Alternative
                    '.easydropdown .selected'  # More general
                ]

                dropdown_found = False
                for selector in dropdown_click_selectors:
                    try:
                        dropdown_element = await self.page.query_selector(selector)
                        if dropdown_element:
                            await dropdown_element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)

                            is_visible = await dropdown_element.is_visible()
                            if is_visible:
                                logger.info(f"Found dropdown element using selector: {selector}")

                                # Click to open the dropdown
                                await dropdown_element.click()
                                logger.info("Clicked dropdown to open it")
                                await asyncio.sleep(1)  # Wait for dropdown to open

                                # Now look for the "e-Facturi cu eroare" option
                                option_selectors = [
                                    '.einvoice-status-restricted .easydropdown ul li:has-text("e-Facturi cu eroare")',
                                    '.dropdown.easydropdown ul li:has-text("e-Facturi cu eroare")',
                                    '.easydropdown ul li:has-text("e-Facturi cu eroare")',
                                    'li:has-text("e-Facturi cu eroare")'
                                ]

                                for option_selector in option_selectors:
                                    try:
                                        option = await self.page.wait_for_selector(option_selector, timeout=5000)
                                        if option:
                                            is_option_visible = await option.is_visible()
                                            if is_option_visible:
                                                logger.info(f"Found option using selector: {option_selector}")
                                                await option.click()
                                                logger.info("Successfully clicked 'e-Facturi cu eroare' option")
                                                dropdown_found = True
                                                break
                                    except Exception as e:
                                        logger.debug(f"Option selector {option_selector} failed: {e}")
                                        continue

                                if dropdown_found:
                                    break
                    except Exception as e:
                        logger.debug(f"Dropdown click selector {selector} failed: {e}")
                        continue

                if not dropdown_found:
                    logger.warning("Could not apply e-Invoice error filter")

            except Exception as e:
                logger.warning(f"Dropdown filter method failed: {e}")

            # Wait for filter to apply and page to update
            await asyncio.sleep(5)  # Give time for the filter to apply and page to update

            logger.info("Successfully navigated to e-Invoice errors section")
            return True

        except Exception as e:
            logger.error(f"Failed to navigate to e-Invoice errors: {e}")
            return False

    async def fetch_einvoice_errors(self) -> List[AddressRecord]:
        """
        Fetch e-Invoice error records from the filtered table.

        Returns:
            List[AddressRecord]: List of records with e-Invoice errors
        """
        try:
            logger.info("Fetching e-Invoice error records")

            # Wait for the table to load
            await asyncio.sleep(3)

            # Look for invoice table rows
            table_selectors = [
                '#invoices_datatable tbody tr.lvl_one_row',
                '#invoices_datatable tbody tr',
                '.datatable tbody tr',
                'table tbody tr'
            ]

            rows = []
            for selector in table_selectors:
                try:
                    rows = await self.page.query_selector_all(selector)
                    if rows:
                        logger.info(f"Found {len(rows)} invoice records using selector: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Table selector {selector} failed: {e}")
                    continue

            if not rows:
                logger.info("No e-Invoice errors found")
                return []

            records = []
            for i, row in enumerate(rows):
                try:
                    # Extract basic information from the row
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 3:
                        # Get all cell texts for analysis
                        cell_texts = []
                        for cell in cells:
                            text = await cell.inner_text()
                            cell_texts.append(text.strip())

                        # Try to find client name and invoice ID
                        client_name = "Unknown Client"
                        invoice_id = f"invoice_{i+1}"

                        # Look for invoice ID (usually a long number)
                        for text in cell_texts:
                            if text.isdigit() and len(text) >= 6:
                                invoice_id = text
                                break

                        # Look for client name (usually contains letters, not just numbers)
                        for text in cell_texts:
                            if text and not text.isdigit() and len(text) > 2 and "€" not in text and "RON" not in text:
                                # Skip dates and amounts
                                if not any(char in text for char in ['/', '.', '-']) or len(text) > 10:
                                    client_name = text
                                    break

                        # Create record
                        record = AddressRecord(
                            client_id=client_name,
                            client_name=client_name,
                            invoice_id=invoice_id,
                            current_address="",  # Will be filled when editing
                            current_city="",
                            current_county=""
                        )
                        records.append(record)
                        logger.debug(f"Created record: {client_name} (Invoice: {invoice_id})")

                except Exception as e:
                    logger.debug(f"Error processing row {i}: {e}")
                    continue

            logger.info(f"Found {len(records)} e-Invoice error records")
            return records

        except Exception as e:
            logger.error(f"Failed to fetch e-Invoice error records: {e}")
            return []

    async def update_invoice_address(self, record: AddressRecord) -> bool:
        """
        Update client address by opening the invoice edit page in a new tab.
        This method finds the dropdown in the table row and clicks "Modifica".

        Args:
            record: AddressRecord with invoice information

        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            logger.info(f"Processing invoice for {record.client_id} (ID: {record.invoice_id})")

            # Store reference to the main invoices tab
            main_tab = self.page

            # Ensure we're on the invoices page
            current_url = main_tab.url
            if 'raport/facturi' not in current_url:
                logger.warning("Not on invoices page, navigating back...")
                await main_tab.goto("https://cloud.smartbill.ro/raport/facturi/", timeout=60000)
                await main_tab.wait_for_load_state('domcontentloaded', timeout=60000)
                await asyncio.sleep(3)

            # Find the dropdown menu for this specific invoice
            # Try to find the dropdown in the table row
            dropdown_selectors = [
                '.actiuni_unelte',  # First available dropdown
                'li.actiuni_unelte',  # More specific
                '.unelte_ico',  # Generic fallback
                'li.unelte_ico'
            ]

            dropdown_found = False

            for selector in dropdown_selectors:
                try:
                    # Get all dropdown elements
                    dropdown_elements = await main_tab.query_selector_all(selector)

                    if dropdown_elements:
                        # For now, just use the first dropdown (we can improve this later)
                        dropdown_element = dropdown_elements[0]

                        # Scroll element into view first
                        await dropdown_element.scroll_into_view_if_needed()
                        await asyncio.sleep(0.5)

                        # Ensure element is visible
                        is_visible = await dropdown_element.is_visible()
                        if is_visible:
                            # Click to show the dropdown
                            await dropdown_element.click()
                            dropdown_found = True
                            logger.info(f"Clicked dropdown using selector: {selector}")
                            break
                except Exception as e:
                    logger.debug(f"Dropdown selector {selector} failed: {e}")
                    continue

            if not dropdown_found:
                logger.error(f"Could not find dropdown menu")
                return False

            # Wait for dropdown to appear
            await asyncio.sleep(2)

            # Look for the "Modifica" (Edit) link in the dropdown
            edit_link_selectors = [
                'a:has-text("Modifica")',  # By text content
                '.dropDown a:has-text("Modifica")',  # In dropdown by text
                'ul.dropDown a:has-text("Modifica")',  # In ul dropdown
                'a[href*="/documente/editare/factura/"]'  # Edit link pattern
            ]

            edit_link = None
            for selector in edit_link_selectors:
                try:
                    edit_link = await main_tab.query_selector(selector)
                    if edit_link:
                        is_visible = await edit_link.is_visible()
                        if is_visible:
                            logger.info(f"Found edit link using selector: {selector}")
                            break
                except Exception as e:
                    logger.debug(f"Edit link selector {selector} failed: {e}")
                    continue

            if not edit_link:
                logger.error(f"Could not find edit link")
                return False

            # Get the edit URL
            edit_url = await edit_link.get_attribute('href')
            if edit_url and not edit_url.startswith('http'):
                edit_url = f"https://cloud.smartbill.ro{edit_url}"

            logger.info(f"Opening edit page: {edit_url}")

            # Open edit page in new tab with increased timeout and retry logic
            edit_tab = await self.context.new_page()

            # Try loading the page with retries
            page_loaded = False
            max_load_attempts = 3

            for attempt in range(1, max_load_attempts + 1):
                try:
                    logger.info(f"Loading edit page (attempt {attempt}/{max_load_attempts}) with 90s timeout...")

                    # Step 1: Navigate to page with extended timeout
                    logger.info(f"Navigating to: {edit_url}")
                    await edit_tab.goto(edit_url, timeout=90000, wait_until='commit')
                    logger.info(f"Navigation committed successfully")

                    # Step 2: Simple approach - just wait for basic DOM content
                    try:
                        await edit_tab.wait_for_load_state('domcontentloaded', timeout=30000)
                        logger.info(f"DOM content loaded")
                    except Exception as dom_e:
                        logger.warning(f"DOM load timeout, but continuing: {dom_e}")

                    # Step 3: Verify page is actually loaded by checking for key elements
                    logger.info(f"Verifying page content is loaded...")

                    # Wait for any of these key elements that indicate the edit page is loaded
                    key_elements = [
                        '#client_name',  # Client name field
                        '#client_details_span',  # Client edit button container
                        '.issuing-client-edit',  # Client edit button class
                        'input[name="client_name"]',  # Alternative client field
                        'form',  # Any form on the page
                        '.form-control'  # Form control elements
                    ]

                    element_found = False
                    for selector in key_elements:
                        try:
                            element = await edit_tab.wait_for_selector(selector, timeout=10000)
                            if element:
                                logger.info(f"Found key element: {selector}")
                                element_found = True
                                break
                        except Exception:
                            continue

                    if element_found:
                        page_loaded = True
                        logger.info(f"Edit page verified as loaded on attempt {attempt}")
                        break
                    else:
                        # Fallback: Check if we're on the right URL
                        current_url = edit_tab.url
                        if 'editare/factura' in current_url:
                            logger.info(f"Page loaded (verified by URL): {current_url}")
                            page_loaded = True
                            break
                        else:
                            raise Exception(f"Page content verification failed - no key elements found")

                except Exception as load_e:
                    logger.warning(f"❌ Page load attempt {attempt} failed: {load_e}")
                    logger.warning(f"❌ Error type: {type(load_e).__name__}")

                    # Check if page might actually be loaded despite the error
                    try:
                        current_url = edit_tab.url
                        logger.info(f"🔍 Current URL after error: {current_url}")

                        if 'editare/factura' in current_url:
                            logger.info(f"🎯 URL suggests page loaded despite error - attempting to continue")
                            page_loaded = True
                            break
                    except Exception:
                        pass

                    if attempt < max_load_attempts:
                        logger.info(f"⏳ Retrying page load in 5 seconds...")
                        await asyncio.sleep(5)
                    else:
                        logger.error(f"❌ All page load attempts failed")
                        await edit_tab.close()
                        await main_tab.bring_to_front()
                        self.page = main_tab
                        return False

            if not page_loaded:
                logger.error("❌ Failed to load edit page after all attempts")
                await edit_tab.close()
                await main_tab.bring_to_front()
                self.page = main_tab
                return False

            # Final verification: Ensure page is interactive
            logger.info("🔍 Final verification - checking if page is interactive...")
            try:
                # Check page title
                page_title = await edit_tab.title()
                logger.info(f"📄 Page title: {page_title}")

                # Check if we can interact with the page
                ready_state = await edit_tab.evaluate('document.readyState')
                logger.info(f"📊 Document ready state: {ready_state}")

                # Verify URL one more time
                final_url = edit_tab.url
                logger.info(f"🌐 Final URL: {final_url}")

                if 'editare/factura' not in final_url:
                    logger.warning(f"⚠️ URL doesn't contain expected path, but continuing anyway")

                logger.info("✅ Page verification complete - proceeding to client edit detection")

            except Exception as verify_e:
                logger.warning(f"⚠️ Page verification failed, but continuing anyway: {verify_e}")

            # Now we're on the edit invoice page - need to click "Edit Client"
            logger.info("Looking for client edit button...")
            logger.info("*** USING ENHANCED DEBUGGING VERSION ***")

            # Give the page extra time to fully load all JavaScript and elements
            logger.info("Waiting for page to fully stabilize...")
            await asyncio.sleep(5)

            # First, let's do comprehensive page analysis
            logger.info("=== PAGE ANALYSIS START ===")

            # Check page loading state
            ready_state = await edit_tab.evaluate('document.readyState')
            logger.info(f"Document ready state: {ready_state}")

            # Check for loading indicators
            loading_indicators = await edit_tab.query_selector_all('.loading, .spinner, [class*="load"]')
            logger.info(f"Found {len(loading_indicators)} potential loading indicators")

            # Wait for page to be fully loaded
            logger.info("Waiting for page to fully load...")
            await asyncio.sleep(5)  # Longer wait

            # Check if client name field exists and its state
            client_field = await edit_tab.query_selector('#client_name')
            if client_field:
                is_disabled = await client_field.get_attribute('disabled')
                field_value = await client_field.input_value()
                logger.info(f"Client field found - disabled: {is_disabled}, value: '{field_value}'")
            else:
                logger.warning("Client name field (#client_name) not found!")

            # Check if the span container exists
            span_container = await edit_tab.query_selector('#client_details_span')
            if span_container:
                span_classes = await span_container.get_attribute('class')
                span_style = await span_container.get_attribute('style')
                span_display = await span_container.evaluate('el => window.getComputedStyle(el).display')
                logger.info(f"Span container found - classes: '{span_classes}', style: '{span_style}', computed display: '{span_display}'")
            else:
                logger.warning("Span container (#client_details_span) not found!")

            # Look for ALL elements that might be the edit button
            all_edit_elements = await edit_tab.query_selector_all('a[href*="edit_client"], [data-original-title*="client"], [title*="client"], .edit, [class*="edit"]')
            logger.info(f"Found {len(all_edit_elements)} potential edit elements on page")

            for i, elem in enumerate(all_edit_elements):
                try:
                    tag_name = await elem.evaluate('el => el.tagName')
                    href = await elem.get_attribute('href')
                    title = await elem.get_attribute('data-original-title') or await elem.get_attribute('title')
                    classes = await elem.get_attribute('class')
                    is_visible = await elem.is_visible()
                    logger.info(f"  Element {i+1}: {tag_name}, href='{href}', title='{title}', classes='{classes}', visible={is_visible}")
                except Exception as e:
                    logger.debug(f"  Element {i+1}: Error analyzing - {e}")

            logger.info("=== PAGE ANALYSIS END ===")

            # Look for client edit button - based on your exact HTML
            client_edit_selectors = [
                '#client_details_span a[href="javascript:edit_client();"]',  # Exact match from your HTML
                'span.issuing-client-edit a[href="javascript:edit_client();"]',  # With span class
                'a[data-original-title="Modifica client"]',  # By tooltip text
                'a[href="javascript:edit_client();"]',  # By href only
                '#client_details_span a',  # Any link in the span
                '.issuing-client-edit a',  # Any link in the class
                'span#client_details_span a',  # More specific span
                'span.issuing-client-edit#client_details_span a'  # Most specific
            ]

            logger.info("=== SELECTOR TESTING START ===")
            client_edit_found = False

            for i, selector in enumerate(client_edit_selectors):
                try:
                    logger.info(f"[{i+1}/{len(client_edit_selectors)}] Trying selector: {selector}")
                    client_edit_btn = await edit_tab.query_selector(selector)

                    if client_edit_btn:
                        logger.info(f"✓ Element FOUND with selector: {selector}")

                        # Get detailed element info
                        try:
                            tag_name = await client_edit_btn.evaluate('el => el.tagName')
                            href = await client_edit_btn.get_attribute('href')
                            classes = await client_edit_btn.get_attribute('class')
                            display_style = await client_edit_btn.evaluate('el => window.getComputedStyle(el).display')
                            visibility = await client_edit_btn.evaluate('el => window.getComputedStyle(el).visibility')
                            opacity = await client_edit_btn.evaluate('el => window.getComputedStyle(el).opacity')

                            logger.info(f"  Tag: {tag_name}, href: '{href}', classes: '{classes}'")
                            logger.info(f"  CSS - display: '{display_style}', visibility: '{visibility}', opacity: '{opacity}'")
                        except Exception as e:
                            logger.debug(f"  Error getting element details: {e}")

                        # Scroll into view first
                        logger.info("  Scrolling element into view...")
                        await client_edit_btn.scroll_into_view_if_needed()
                        await asyncio.sleep(1)

                        is_visible = await client_edit_btn.is_visible()
                        logger.info(f"  Element is_visible(): {is_visible}")

                        # Try clicking regardless of visibility check (sometimes is_visible() is unreliable)
                        logger.info(f"Attempting to click element (visible={is_visible})...")
                        try:
                            await client_edit_btn.click()
                            client_edit_found = True
                            logger.info("✓ Successfully clicked client edit button")
                            break
                        except Exception as click_e:
                            logger.warning(f"Direct click failed: {click_e}")

                            # Try JavaScript click as fallback
                            try:
                                logger.info("Trying JavaScript click...")
                                await client_edit_btn.evaluate('el => el.click()')
                                client_edit_found = True
                                logger.info("✓ Successfully clicked client edit button using JavaScript")
                                break
                            except Exception as js_click_e:
                                logger.warning(f"JavaScript click also failed: {js_click_e}")

                        # If direct click failed and element not visible, try hover approach
                        if not is_visible:
                            logger.warning(f"✗ Element found but NOT VISIBLE with selector: {selector}")

                            # Try to make it visible by hovering over the client field
                            logger.info("  Attempting hover over client field to trigger visibility...")
                            try:
                                client_field = await edit_tab.query_selector('#client_name')
                                if client_field:
                                    logger.info("  Hovering over #client_name...")
                                    await client_field.hover()
                                    await asyncio.sleep(2)

                                    # Check visibility again
                                    is_visible_after_hover = await client_edit_btn.is_visible()
                                    logger.info(f"  Element visible after hover: {is_visible_after_hover}")

                                    if is_visible_after_hover:
                                        logger.info(f"✓ SUCCESS: Client edit button became visible after hover!")
                                        try:
                                            await client_edit_btn.click()
                                            client_edit_found = True
                                            logger.info("✓ Successfully clicked client edit button after hover")
                                            break
                                        except Exception as click_e:
                                            logger.error(f"✗ Click failed after hover: {click_e}")
                                            continue
                                    else:
                                        logger.warning("✗ Element still not visible after hover")
                                else:
                                    logger.warning("✗ Client field (#client_name) not found for hover")
                            except Exception as hover_e:
                                logger.error(f"✗ Hover attempt failed: {hover_e}")
                    else:
                        logger.warning(f"✗ Element NOT FOUND with selector: {selector}")

                except Exception as e:
                    logger.error(f"✗ Selector {selector} failed with error: {e}")
                    continue

            logger.info("=== SELECTOR TESTING END ===")

            # If still not found, try JavaScript approach
            if not client_edit_found:
                logger.info("=== TRYING JAVASCRIPT APPROACH ===")
                try:
                    # Try to call the edit_client function directly
                    logger.info("Attempting to call edit_client() function directly...")
                    result = await edit_tab.evaluate('typeof edit_client === "function" ? edit_client() : "function not found"')
                    logger.info(f"Direct function call result: {result}")
                    if result != "function not found":
                        client_edit_found = True
                        logger.info("✓ Successfully called edit_client() directly")
                except Exception as js_e:
                    logger.error(f"✗ JavaScript approach failed: {js_e}")

                logger.info("=== JAVASCRIPT APPROACH END ===")

            if not client_edit_found:
                logger.error("Could not find client edit button")
                await edit_tab.close()
                await main_tab.bring_to_front()
                self.page = main_tab
                return False

            # Wait for client edit form/popup to appear
            await asyncio.sleep(3)

            # Extract current address and county from the client form
            logger.info("Extracting current client address...")

            try:
                # Look for address fields in the client edit form
                address_selectors = [
                    'input[name="address"]',
                    'input[id="address"]',
                    'textarea[name="address"]',
                    'input[placeholder*="adres"]',
                    'input[placeholder*="Adres"]'
                ]

                county_selectors = [
                    'select[name="county"]',
                    'select[name="judet"]',
                    'input[name="county"]',
                    'input[name="judet"]',
                    'select[id="county"]',
                    'select[id="judet"]'
                ]

                current_address = ""
                current_county = ""

                # Extract current address
                for selector in address_selectors:
                    try:
                        address_field = await edit_tab.query_selector(selector)
                        if address_field:
                            current_address = await address_field.input_value()
                            if current_address:
                                logger.info(f"Found current address: {current_address}")
                                break
                    except Exception:
                        continue

                # Extract current county
                for selector in county_selectors:
                    try:
                        county_field = await edit_tab.query_selector(selector)
                        if county_field:
                            # For select elements, get selected option text
                            if selector.startswith('select'):
                                current_county = await county_field.evaluate('el => el.options[el.selectedIndex]?.text || el.value')
                            else:
                                current_county = await county_field.input_value()
                            if current_county:
                                logger.info(f"Found current county: {current_county}")
                                break
                    except Exception:
                        continue

                # Update record with current address info
                record.current_address = current_address
                record.current_county = current_county

                # If county is Bucuresti, geocode to get sector
                if current_county.lower() in ['bucuresti', 'bucharest'] and current_address:
                    logger.info(f"Bucuresti address detected, geocoding: {current_address}")

                    # Import geocoding service here to avoid circular imports
                    try:
                        from .geocoding_service import GeocodingService
                        geocoding = GeocodingService(self.config)

                        # Geocode the address to get sector
                        geocoding_result = await geocoding.geocode_address(
                            current_address, "", "Bucuresti"
                        )

                        if geocoding_result and len(geocoding_result) == 3:
                            new_address, new_city, new_county = geocoding_result
                        else:
                            logger.warning(f"Invalid geocoding result: {geocoding_result}")
                            new_address, new_city, new_county = None, None, None

                    except Exception as geocoding_error:
                        logger.error(f"Geocoding failed: {geocoding_error}")
                        new_address, new_city, new_county = None, None, None

                    if new_city and "Sector" in new_city:
                        logger.info(f"Found sector: {new_city}")
                        record.new_city = new_city
                        record.new_address = new_address or current_address
                        record.new_county = new_county or current_county

                        # Update the city field with the sector
                        city_selectors = [
                            'select[name="city"]',
                            'input[name="city"]',
                            'select[name="oras"]',
                            'input[name="oras"]',
                            'select[id="city"]',
                            'input[id="city"]'
                        ]

                        city_updated = False
                        for selector in city_selectors:
                            try:
                                city_field = await edit_tab.query_selector(selector)
                                if city_field:
                                    if selector.startswith('select'):
                                        # For select, try to find option with sector name
                                        await city_field.select_option(label=new_city)
                                    else:
                                        # For input, fill with sector name
                                        await city_field.fill(new_city)

                                    logger.info(f"Updated city field with: {new_city}")
                                    city_updated = True
                                    break
                            except Exception as e:
                                logger.debug(f"City field update failed for {selector}: {e}")
                                continue

                        if city_updated:
                            # Save client details
                            save_selectors = [
                                'button:has-text("Salvare")',
                                'button:has-text("Save")',
                                'input[type="submit"]',
                                'button[type="submit"]',
                                '.save-btn',
                                '#save-client'
                            ]

                            for selector in save_selectors:
                                try:
                                    save_btn = await edit_tab.query_selector(selector)
                                    if save_btn:
                                        is_visible = await save_btn.is_visible()
                                        if is_visible:
                                            await save_btn.click()
                                            logger.info("Clicked save client button")
                                            await asyncio.sleep(2)
                                            break
                                except Exception:
                                    continue

                            logger.info(f"Successfully updated address for {record.client_id}")
                        else:
                            logger.warning(f"Could not find city field to update")
                    else:
                        logger.info(f"No sector found for address: {current_address}")
                else:
                    logger.info(f"Not a Bucuresti address or no address found: {current_county}")

            except Exception as e:
                logger.error(f"Error processing client address: {e}")

            # Close the edit tab and return to main tab
            await edit_tab.close()
            await main_tab.bring_to_front()
            self.page = main_tab
            logger.info("Returned to main invoices tab")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to update invoice address for {record.client_id}: {e}")
            logger.error(f"❌ Error type: {type(e).__name__}")
            logger.error(f"❌ Error details: {str(e)}")

            # Ensure we clean up the edit tab if it exists
            try:
                if 'edit_tab' in locals():
                    await edit_tab.close()
                    logger.info("🧹 Cleaned up edit tab")
            except Exception as cleanup_e:
                logger.debug(f"Tab cleanup error: {cleanup_e}")

            # Ensure we return to main tab
            try:
                await main_tab.bring_to_front()
                self.page = main_tab
                logger.info("🔄 Returned to main tab after error")
            except Exception as return_e:
                logger.debug(f"Return to main tab error: {return_e}")

            return False
