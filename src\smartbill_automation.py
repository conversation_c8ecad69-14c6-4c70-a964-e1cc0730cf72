"""
SmartBill automation module for e-Invoice error correction.
This module handles browser automation for SmartBill Cloud platform.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple
import pyotp

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

from .config import Config
from .models import AddressRecord

logger = logging.getLogger(__name__)


class SmartBillAutomation:
    """Handles SmartBill browser automation for e-Invoice error correction."""

    def __init__(self, config: Config):
        self.config = config
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.totp = pyotp.TOTP(config.totp_secret)
        self.einvoice_modal_handled = False  # Track if e-Invoice notification modal was handled
        self.filter_already_applied_via_modal = False  # Track if filter is still applied after modal navigation
        self.processed_invoices = set()  # Track processed invoices to prevent loops

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_browser()

    async def start_browser(self) -> None:
        """Initialize and start the browser."""
        playwright = await async_playwright().start()

        self.browser = await playwright.chromium.launch(
            headless=self.config.headless,
            args=[
                '--no-sandbox', 
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ]
        )

        self.context = await self.browser.new_context(
             # This makes it use the full window size
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        self.page = await self.context.new_page()
        self.page.set_default_timeout(self.config.browser_timeout)

        logger.info(f"Browser started successfully (headless: {self.config.headless})")

    async def close_browser(self) -> None:
        """Close the browser and cleanup."""
        try:
            if self.context:
                await self.context.close()
                logger.debug("Browser context closed")
        except Exception as e:
            logger.debug(f"Error closing context: {e}")

        try:
            if self.browser:
                await self.browser.close()
                logger.info("Browser closed")
        except Exception as e:
            logger.debug(f"Error closing browser: {e}")

        # Give time for cleanup
        await asyncio.sleep(1)

    async def handle_cookie_popup(self) -> None:
        """Handle cookie consent popup if it appears."""
        try:
            logger.info("Checking for cookie consent popup...")

            # Wait a bit for any popups to load
            await asyncio.sleep(2)

            # Common selectors for OneTrust and other cookie popups
            cookie_selectors = [
                '#onetrust-accept-btn-handler',  # Primary OneTrust selector
                'button:has-text("Accept toate cookie-urile")',  # Exact text match
                'button:has-text("Accept")',
                'button:has-text("Accept All")',
                'button:has-text("Acceptă")',
                'button:has-text("Acceptă toate")',
                '.onetrust-close-btn-handler',
                '[data-testid="accept-cookies"]',
                '.cookie-accept',
                '.accept-cookies',
                '#cookie-accept'
            ]

            for selector in cookie_selectors:
                try:
                    # Check if element exists and is visible
                    element = await self.page.query_selector(selector)
                    if element:
                        # Scroll into view first
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(0.3)

                        is_visible = await element.is_visible()
                        if is_visible:
                            logger.info(f"Found cookie button with selector: {selector}")
                            await element.click()
                            logger.info(f"Successfully clicked cookie accept button")
                            await asyncio.sleep(2)  # Wait for popup to close
                            return
                        else:
                            logger.debug(f"Cookie button found but not visible: {selector}")
                    else:
                        logger.debug(f"Cookie button not found: {selector}")
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue

            logger.info("No cookie popup detected or already handled")

        except Exception as e:
            logger.warning(f"Error handling cookie popup: {e}")

    async def login(self) -> bool:
        """
        Perform login to SmartBill with MFA support.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("Starting SmartBill login process")

            # Navigate to SmartBill Cloud login page
            logger.info("Navigating to SmartBill login page...")
            await self.page.goto("https://cloud.smartbill.ro/auth/login/", wait_until='domcontentloaded')

            # Wait for page to load
            logger.info("Waiting for page to load...")
            try:
                await self.page.wait_for_load_state('domcontentloaded', timeout=60000)
                logger.info("DOM content loaded")
                await asyncio.sleep(3)  # Give time for any dynamic content
            except Exception as e:
                logger.warning(f"Page load timeout, continuing anyway: {e}")

            # Handle cookie popup if it appears
            await self.handle_cookie_popup()

            # Wait a bit more to ensure page is fully loaded
            await asyncio.sleep(2)

            # Fill username using the exact selector from the form
            try:
                await self.page.fill('#id_username', self.config.smartbill_username)
                logger.debug("Username filled successfully")
            except Exception as e:
                logger.error(f"Failed to fill username: {e}")
                raise Exception("Could not find username input field")

            # Fill password using the exact selector from the form
            try:
                await self.page.fill('#id_password', self.config.smartbill_password)
                logger.debug("Password filled successfully")
            except Exception as e:
                logger.error(f"Failed to fill password: {e}")
                raise Exception("Could not find password input field")

            # Click login button using the exact selector from the form
            try:
                await self.page.click('#cloud_login_btn')
                logger.debug("Login button clicked successfully")
            except Exception as e:
                logger.error(f"Failed to click login button: {e}")
                raise Exception("Could not find login button")

            # Wait for page response after login click
            logger.info("Waiting for page response after login...")
            await asyncio.sleep(3)  # Give time for the page to respond

            # Check if MFA is required (2FA field appears on same page)
            try:
                logger.info("Checking for MFA/2FA requirement...")

                # Look for TOTP/MFA input field with the exact selector
                totp_selectors = [
                    '#2fa_code',  # Exact selector from your HTML
                    'input[name="2fa_code"]',  # Alternative by name
                    'input[id="2fa_code"]',  # Alternative by id
                    'input[placeholder*="Codul de securitate"]',  # By placeholder text
                    'input[name="totp"]',
                    'input[name="code"]',
                    'input[name="verification_code"]',
                    'input[placeholder*="cod"]',
                    'input[placeholder*="code"]'
                ]

                totp_input = None
                for selector in totp_selectors:
                    try:
                        # Check if the element exists first
                        element = await self.page.query_selector(selector)
                        if element:
                            is_visible = await element.is_visible()
                            if is_visible:
                                totp_input = element
                                logger.info(f"Found MFA input field using selector: {selector}")
                                break
                            else:
                                logger.debug(f"MFA field found but not visible: {selector}")
                        else:
                            logger.debug(f"MFA field not found: {selector}")
                    except Exception as e:
                        logger.debug(f"Error checking selector {selector}: {e}")
                        continue

                # If not found immediately, wait a bit and try again
                if not totp_input:
                    logger.info("2FA field not found immediately, waiting and retrying...")
                    await asyncio.sleep(2)

                    for selector in totp_selectors:
                        try:
                            element = await self.page.query_selector(selector)
                            if element:
                                is_visible = await element.is_visible()
                                if is_visible:
                                    totp_input = element
                                    logger.info(f"Found MFA input field on retry using selector: {selector}")
                                    break
                        except Exception:
                            continue

                if totp_input:
                    logger.info("MFA required, generating TOTP code")

                    # Generate TOTP code
                    totp_code = self.totp.now()
                    logger.info(f"Generated TOTP code: {totp_code}")

                    # Validate TOTP code format (should be 6 digits)
                    if not totp_code or len(totp_code) != 6 or not totp_code.isdigit():
                        logger.error(f"Invalid TOTP code format: '{totp_code}' (should be 6 digits)")
                        # Try generating again
                        await asyncio.sleep(1)
                        totp_code = self.totp.now()
                        logger.info(f"Regenerated TOTP code: {totp_code}")

                        if not totp_code or len(totp_code) != 6 or not totp_code.isdigit():
                            logger.error(f"TOTP code still invalid: '{totp_code}'")
                            raise Exception("Cannot generate valid TOTP code")

                    # Focus on the input field first
                    await totp_input.focus()
                    await asyncio.sleep(0.5)

                    # Clear and fill the MFA input using element methods
                    try:
                        # Use the element handle directly since we already have it
                        # First clear by selecting all and deleting
                        await totp_input.click(click_count=3)  # Select all
                        await self.page.keyboard.press('Delete')  # Delete selected
                        await asyncio.sleep(0.3)

                        # Type the TOTP code character by character for better reliability
                        await self.page.keyboard.type(totp_code)
                        logger.info("TOTP code entered using keyboard.type()")

                    except Exception as e:
                        logger.warning(f"Keyboard method failed, trying alternative: {e}")
                        try:
                            # Alternative: use JavaScript to set the value
                            await self.page.evaluate(f'''
                                const input = document.querySelector('input[name="2fa_code"]') || document.getElementById('2fa_code');
                                if (input) {{
                                    input.value = '{totp_code}';
                                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                }}
                            ''')
                            logger.info("TOTP code entered using JavaScript")
                        except Exception as e2:
                            logger.error(f"All input methods failed: {e2}")

                    # Wait a moment for the input to register
                    await asyncio.sleep(1)

                    # Verify the code was entered using JavaScript
                    try:
                        entered_value = await self.page.evaluate('''
                            const input = document.querySelector('input[name="2fa_code"]') || document.getElementById('2fa_code');
                            return input ? input.value : '';
                        ''')
                        logger.info(f"Value in 2FA field: '{entered_value}'")

                        # Check if the entered value matches what we expect
                        if entered_value != totp_code:
                            logger.warning(f"Entered value '{entered_value}' doesn't match expected '{totp_code}'")

                    except Exception as e:
                        logger.warning(f"Could not verify entered value: {e}")

                    # Submit MFA code - look for the exact submit button
                    mfa_submit_selectors = [
                        '#confirm_2fa_code',  # Exact button from your HTML
                        'a:has-text("Continua")',  # By text
                        '.twofa-confirmation',  # By class
                        'a.btn.btn-primary:has-text("Continua")',  # More specific
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button:has-text("Continue")',
                        'button:has-text("Continuă")',
                        '#cloud_login_btn'  # Fallback
                    ]

                    submit_clicked = False
                    for selector in mfa_submit_selectors:
                        try:
                            submit_button = await self.page.query_selector(selector)
                            if submit_button:
                                is_visible = await submit_button.is_visible()
                                if is_visible:
                                    logger.info(f"Found submit button: {selector}")
                                    await submit_button.click()
                                    logger.info(f"Clicked MFA submit button using selector: {selector}")
                                    submit_clicked = True
                                    break
                                else:
                                    logger.debug(f"Submit button found but not visible: {selector}")
                            else:
                                logger.debug(f"Submit button not found: {selector}")
                        except Exception as e:
                            logger.debug(f"Error with submit selector {selector}: {e}")
                            continue

                    if not submit_clicked:
                        # Try pressing Enter as fallback
                        logger.info("No submit button found, trying Enter key on 2FA input")
                        await totp_input.press('Enter')

                    # Wait for page to process MFA
                    logger.info("Waiting for MFA processing...")
                    await asyncio.sleep(5)

                    try:
                        await self.page.wait_for_load_state('domcontentloaded', timeout=30000)
                    except Exception as e:
                        logger.warning(f"Page load timeout after MFA, continuing: {e}")
                else:
                    logger.info("No MFA input field found")

            except Exception as e:
                logger.warning(f"Error handling MFA: {e}")

            # After MFA (if required), wait for successful login
            logger.info("Checking for successful login...")

            # Wait for successful login - check for dashboard or main application
            success_indicators = [
                '.main-content',
                '.dashboard',
                '[data-testid="dashboard"]',
                '.navbar',
                '.sidebar',
                'a[href*="raport"]',
                'a[href*="facturi"]'
            ]

            login_successful = False

            # Wait a bit for redirect after MFA
            await asyncio.sleep(3)

            for indicator in success_indicators:
                try:
                    await self.page.wait_for_selector(indicator, timeout=10000)
                    login_successful = True
                    logger.info(f"Login success detected using indicator: {indicator}")
                    break
                except Exception:
                    continue

            if not login_successful:
                # Check if we're redirected to a dashboard URL
                current_url = self.page.url
                logger.info(f"Current URL after login: {current_url}")
                if 'cloud.smartbill.ro' in current_url and 'login' not in current_url:
                    login_successful = True
                    logger.info(f"Login success detected by URL change: {current_url}")

            if login_successful:
                logger.info("Login completed successfully")

                # Check for cookie consent popup after login (can interfere with modals)
                logger.info("=== POST-LOGIN COOKIE CONSENT CHECK ===")
                await self._check_and_handle_post_login_cookies()

                # Check for e-Invoice notification modal immediately after login
                logger.info("=== E-INVOICE NOTIFICATION MODAL CHECK ===")
                modal_handled = await self._check_and_handle_einvoice_notification_modal()
                if modal_handled:
                    logger.info("=== MODAL HANDLED - DIRECT NAVIGATION SUCCESSFUL ===")
                else:
                    logger.info("=== NO MODAL - WILL USE MANUAL NAVIGATION ===")

                return True
            else:
                current_url = self.page.url
                logger.error(f"Login verification failed. Current URL: {current_url}")

                # Check if we're still on login page with 2FA
                if 'login' in current_url:
                    totp_field = await self.page.query_selector('#2fa_code')
                    if totp_field:
                        logger.error("Still on login page with 2FA field visible - MFA may have failed")
                    else:
                        logger.error("Still on login page but no 2FA field - check credentials")

                raise Exception("Could not verify successful login")

        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False

    async def _check_and_handle_post_login_cookies(self) -> bool:
        """
        Check for and handle cookie consent popup that may appear after login.
        This can interfere with modal interactions.

        Returns:
            bool: True if cookie popup was found and handled, False otherwise
        """
        try:
            logger.info("Checking for post-login cookie consent popup...")

            # Wait a moment for any cookie popups to appear
            await asyncio.sleep(2)

            # Define selectors for cookie consent elements
            cookie_selectors = [
                '#onetrust-accept-btn-handler',
                '.cookie-consent button',
                'button:has-text("Accept")',
                'button:has-text("Accepta")',
                'button:has-text("Accept All")',
                'button:has-text("Accepta toate")',
                '.onetrust-close-btn-handler',
                '#onetrust-banner-sdk button'
            ]

            cookie_found = False
            cookie_element = None

            # Try to find cookie consent elements
            for selector in cookie_selectors:
                try:
                    cookie_element = await self.page.query_selector(selector)
                    if cookie_element:
                        is_visible = await cookie_element.is_visible()
                        logger.info(f"Cookie element found with selector '{selector}', visible: {is_visible}")
                        if is_visible:
                            cookie_found = True
                            break
                except Exception as e:
                    logger.debug(f"Cookie selector '{selector}' failed: {e}")
                    continue

            if not cookie_found:
                logger.info("No post-login cookie consent popup detected")
                return False

            # Click the cookie consent button
            try:
                await cookie_element.click()
                logger.info("Post-login cookie consent popup handled successfully")

                # Wait for popup to disappear
                await asyncio.sleep(3)
                return True

            except Exception as e:
                logger.warning(f"Failed to click post-login cookie consent: {e}")
                return False

        except Exception as e:
            logger.warning(f"Error checking for post-login cookie consent: {e}")
            return False

    async def _check_and_handle_einvoice_notification_modal(self) -> bool:
        """
        Check for and handle the e-Invoice notification modal that appears after login.

        Returns:
            bool: True if modal was found and handled, False otherwise
        """
        try:
            logger.info("Checking for e-Invoice notification modal...")

            # Wait a moment for any modals to appear
            await asyncio.sleep(2)

            # Define selectors for the e-Invoice notification modal
            modal_selectors = [
                'div.box.Warning:has-text("Verifica e-Facturile")',
                'div:has-text("Au aparut probleme care privesc e-Facturile")',
                'a[onclick*="filter_einvoices_with_problems"]'
            ]

            modal_found = False
            modal_element = None

            # Try to find the modal using different selectors
            for selector in modal_selectors:
                try:
                    modal_element = await self.page.query_selector(selector)
                    if modal_element:
                        is_visible = await modal_element.is_visible()
                        logger.info(f"Modal element found with selector '{selector}', visible: {is_visible}")
                        if is_visible:
                            modal_found = True
                            break
                except Exception as e:
                    logger.debug(f"Modal selector '{selector}' failed: {e}")
                    continue

            if not modal_found:
                logger.info("No e-Invoice notification modal detected - proceeding with manual navigation")
                return False

            # Verify modal content
            try:
                # Check for expected header text
                header_element = await self.page.query_selector('div.box.Warning .text-bold:has-text("Verifica e-Facturile")')
                if not header_element:
                    logger.warning("Modal found but header text 'Verifica e-Facturile' not found")

                # Check for expected body text
                body_element = await self.page.query_selector('div:has-text("Au aparut probleme care privesc e-Facturile emise in ultimele 7 zile")')
                if not body_element:
                    logger.warning("Modal found but expected body text not found")

                logger.info("E-Invoice notification modal content verified")

            except Exception as e:
                logger.warning(f"Could not verify modal content: {e}")

            # Look for the action link - prioritize the correct "Vezi facturile" link
            action_selectors = [
                'a[onclick="window.filter_einvoices_with_problems()"]',
                'a:has-text("Vezi facturile.")',
                'a[onclick*="filter_einvoices_with_problems"]',
                'a:has-text("Vezi facturile")',  # Without period
                'a:has-text("facturile")',  # Partial match for invoices
            ]

            action_link = None
            for selector in action_selectors:
                try:
                    action_link = await self.page.query_selector(selector)
                    if action_link:
                        is_visible = await action_link.is_visible()
                        link_text = await action_link.text_content()
                        onclick_attr = await action_link.get_attribute('onclick')
                        href_attr = await action_link.get_attribute('href')
                        logger.info(f"Action link found with selector '{selector}':")
                        logger.info(f"  Visible: {is_visible}, Text: '{link_text}'")
                        logger.info(f"  onclick: '{onclick_attr}', href: '{href_attr}'")
                        if is_visible:
                            break
                except Exception as e:
                    logger.debug(f"Action link selector '{selector}' failed: {e}")
                    continue

            # If no specific action link found, search all links in the modal for the correct one
            if not action_link:
                try:
                    all_links = await self.page.query_selector_all('div.box.Warning a')
                    logger.info(f"Found {len(all_links)} links in warning modal:")
                    for i, link in enumerate(all_links):
                        try:
                            text = await link.text_content()
                            onclick = await link.get_attribute('onclick')
                            href = await link.get_attribute('href')
                            visible = await link.is_visible()
                            logger.info(f"  Link {i+1}: text='{text}', onclick='{onclick}', href='{href}', visible={visible}")

                            # Look for the correct link based on text content or onclick attribute
                            if visible and (
                                'facturile' in text.lower() or
                                'filter_einvoices_with_problems' in (onclick or '') or
                                ('vezi' in text.lower() and 'facturile' in text.lower())
                            ):
                                logger.info(f"Found correct action link: '{text}'")
                                action_link = link
                                break
                            # Also accept "Vezi detalii" as it leads to the second modal
                            elif visible and 'vezi detalii' in text.lower():
                                logger.info(f"Found 'Vezi detalii' link - will use for two-step modal process: '{text}'")
                                action_link = link
                                # Don't break here, continue looking for direct "facturile" link first
                        except Exception:
                            pass
                except Exception as e:
                    logger.debug(f"Could not enumerate modal links: {e}")

            if not action_link:
                logger.warning("Modal found but action link not found - proceeding with manual navigation")
                return False

            # Click the action link (first step)
            try:
                await action_link.click()
                logger.info("Clicked first modal link - waiting for second modal to appear...")

                # Wait for the second modal to appear
                await asyncio.sleep(3)

                # Look for the second modal with "Vezi Facturile" button
                second_modal_selectors = [
                    'button:has-text("Vezi Facturile")',
                    'a:has-text("Vezi Facturile")',
                    'button:has-text("Vezi facturile")',
                    'a:has-text("Vezi facturile")',
                    '.modal button:has-text("Vezi")',
                    '.modal a:has-text("Vezi")'
                ]

                second_action_element = None
                for selector in second_modal_selectors:
                    try:
                        second_action_element = await self.page.query_selector(selector)
                        if second_action_element:
                            is_visible = await second_action_element.is_visible()
                            element_text = await second_action_element.text_content()
                            logger.info(f"Second modal element found with selector '{selector}': visible={is_visible}, text='{element_text}'")
                            if is_visible:
                                break
                    except Exception as e:
                        logger.debug(f"Second modal selector '{selector}' failed: {e}")
                        continue

                if not second_action_element:
                    logger.warning("Second modal with 'Vezi Facturile' button not found - checking if navigation happened anyway")

                    # Check if we navigated directly
                    current_url = self.page.url
                    if 'raport/facturi' in current_url or 'facturi' in current_url:
                        logger.info("Direct navigation to invoices page detected")
                        self.einvoice_modal_handled = True
                        return True
                    else:
                        logger.warning("No second modal and no direct navigation - proceeding with manual navigation")
                        return False

                # Click the "Vezi Facturile" button in the second modal
                try:
                    logger.info("Clicking 'Vezi Facturile' button in second modal...")
                    await second_action_element.click()
                    logger.info("'Vezi Facturile' button clicked - waiting for page navigation and loading...")

                    # Wait for navigation to complete with proper loading state detection
                    navigation_success = await self._wait_for_invoices_page_to_load()

                    if navigation_success:
                        logger.info("Successfully navigated to invoices page via e-Invoice notification modal")
                        self.einvoice_modal_handled = True
                        return True
                    else:
                        logger.warning("Navigation or page loading failed - proceeding with manual navigation")
                        return False

                except Exception as e:
                    logger.warning(f"Failed to click second modal button: {e} - proceeding with manual navigation")
                    return False

            except Exception as e:
                logger.warning(f"Failed to click first modal action link: {e} - proceeding with manual navigation")
                return False

        except Exception as e:
            logger.warning(f"Error checking for e-Invoice notification modal: {e} - proceeding with manual navigation")
            return False

    async def _wait_for_invoices_page_to_load(self) -> bool:
        """
        Wait for the invoices page to fully load after clicking 'Vezi Facturile'.

        Returns:
            bool: True if page loaded successfully, False if timeout or error
        """
        try:
            logger.info("=== INVOICES PAGE LOADING DETECTION START ===")

            # Step 1: Wait for URL to change and stabilize
            logger.info("Step 1: Waiting for URL to change to invoices page...")
            url_timeout = 15  # seconds
            start_time = time.time()
            initial_filter_detected = False

            while time.time() - start_time < url_timeout:
                current_url = self.page.url
                logger.info(f"Current URL: {current_url}")

                # Check if we initially get a filtered URL
                if not initial_filter_detected and self._check_if_filter_already_applied_via_url(current_url):
                    logger.info("Initial filter detected in URL - noting for later verification")
                    initial_filter_detected = True

                if 'raport/facturi' in current_url or 'facturi' in current_url:
                    logger.info("URL changed to invoices page successfully")
                    break

                await asyncio.sleep(1)
            else:
                logger.warning("URL did not change to invoices page within timeout")
                return False

            # Step 2: Wait for DOM content to be loaded
            logger.info("Step 2: Waiting for DOM content to load...")
            try:
                await self.page.wait_for_load_state('domcontentloaded', timeout=20000)
                logger.info("DOM content loaded successfully")
            except Exception as e:
                logger.warning(f"DOM load timeout: {e}")
                # Continue anyway, might still work

            # Step 3: Wait for key invoice elements to be present
            logger.info("Step 3: Waiting for key invoice page elements...")

            # Key elements that should be present on invoices page
            key_elements = [
                '#invoices_datatable',  # Main invoices table
                '.einvoice-status-restricted',  # Filter dropdown container
                '.dropdown.easydropdown',  # Filter dropdown
            ]

            elements_found = 0
            element_timeout = 20  # seconds
            start_time = time.time()

            while time.time() - start_time < element_timeout and elements_found < len(key_elements):
                for i, selector in enumerate(key_elements):
                    if i < elements_found:
                        continue  # Already found this element

                    try:
                        element = await self.page.query_selector(selector)
                        if element:
                            is_visible = await element.is_visible()
                            logger.info(f"Key element found: {selector}, visible: {is_visible}")
                            if is_visible:
                                elements_found += 1
                    except Exception as e:
                        logger.debug(f"Error checking element {selector}: {e}")

                if elements_found < len(key_elements):
                    await asyncio.sleep(1)

            logger.info(f"Found {elements_found}/{len(key_elements)} key elements")

            # Step 4: Wait for loading indicators to disappear
            logger.info("Step 4: Waiting for loading indicators to disappear...")

            loading_selectors = [
                '.loading',
                '.spinner',
                '.loader',
                '[data-loading="true"]',
                '.dataTables_processing',
                '.dataTables_wrapper .dataTables_processing'
            ]

            loading_timeout = 15  # seconds
            start_time = time.time()

            while time.time() - start_time < loading_timeout:
                loading_found = False

                for selector in loading_selectors:
                    try:
                        loading_element = await self.page.query_selector(selector)
                        if loading_element:
                            is_visible = await loading_element.is_visible()
                            if is_visible:
                                logger.info(f"Loading indicator still visible: {selector}")
                                loading_found = True
                                break
                    except Exception:
                        continue

                if not loading_found:
                    logger.info("No loading indicators detected - page appears ready")
                    break

                await asyncio.sleep(1)

            # Step 5: Final verification - check if filter dropdown is functional
            logger.info("Step 5: Verifying filter dropdown functionality...")

            try:
                filter_dropdown = await self.page.query_selector('.einvoice-status-restricted .dropdown.easydropdown .selected')
                if filter_dropdown:
                    is_visible = await filter_dropdown.is_visible()
                    is_enabled = await filter_dropdown.is_enabled()
                    logger.info(f"Filter dropdown found: visible={is_visible}, enabled={is_enabled}")

                    if is_visible and is_enabled:
                        logger.info("Filter dropdown is functional")
                    else:
                        logger.warning("Filter dropdown found but not functional")
                else:
                    logger.warning("Filter dropdown not found")
            except Exception as e:
                logger.warning(f"Error checking filter dropdown: {e}")

            # Step 6: Additional wait to ensure everything is stable
            logger.info("Step 6: Final stabilization wait...")
            await asyncio.sleep(5)

            # Final URL verification
            final_url = self.page.url
            logger.info(f"Final URL after loading: {final_url}")

            # Check if filter is still applied after page stabilization
            final_filter_applied = self._check_if_filter_already_applied_via_url(final_url)

            if initial_filter_detected and not final_filter_applied:
                logger.info("Filter was initially detected but lost during page loading - manual application will be needed")
            elif final_filter_applied:
                logger.info("Filter is still applied after page loading - manual application can be skipped")
                # Store this information for the navigation method
                self.filter_already_applied_via_modal = True

            if 'raport/facturi' in final_url or 'facturi' in final_url:
                logger.info("=== INVOICES PAGE LOADING DETECTION COMPLETE - SUCCESS ===")
                return True
            else:
                logger.warning("=== INVOICES PAGE LOADING DETECTION COMPLETE - FAILED ===")
                return False

        except Exception as e:
            logger.error(f"Error during invoices page loading detection: {e}")
            return False

    def _check_if_filter_already_applied_via_url(self, url: str) -> bool:
        """
        Check if the e-Invoice error filter is already applied based on URL parameters.

        Args:
            url: Current page URL

        Returns:
            bool: True if filter is already applied, False otherwise
        """
        try:
            logger.info(f"Checking URL for pre-applied filter: {url}")

            # Known URL parameters that indicate e-Invoice error filter is applied
            filter_indicators = [
                'no-survey=true',  # Primary indicator after modal navigation (filter is applied)
                'problemsOnly=true',  # Initial indicator from modal navigation
                'einvoicing_status=nok',  # Alternative parameter format
                'filter=einvoice_errors',  # Another possible format
                'status=error',  # Generic error status
                'einvoice_filter=error'  # Explicit e-invoice error filter
            ]

            url_lower = url.lower()

            for indicator in filter_indicators:
                if indicator.lower() in url_lower:
                    if 'no-survey=true' in indicator.lower():
                        logger.info(f"Filter indicator found in URL: '{indicator}' - e-Invoice error filter is active via modal")
                    else:
                        logger.info(f"Filter indicator found in URL: '{indicator}'")
                    return True

            # Additional check for URL path that might indicate filtered view
            if 'einvoice' in url_lower and ('error' in url_lower or 'problem' in url_lower):
                logger.info("E-Invoice error path detected in URL")
                return True

            logger.info("No filter indicators found in URL")
            return False

        except Exception as e:
            logger.warning(f"Error checking URL for filter parameters: {e}")
            return False

    async def _apply_einvoice_error_filter_manually(self, page) -> bool:
        """
        Apply the e-Invoice error filter manually using the dropdown.

        Args:
            page: The page object to apply the filter on

        Returns:
            bool: True if filter applied successfully, False otherwise
        """
        try:
            logger.info("Applying e-Invoice error filter manually...")

            # Click the dropdown to open it
            dropdown_click_selectors = [
                '.einvoice-status-restricted .dropdown.easydropdown .selected',
                '.einvoice-status-restricted .dropdown.easydropdown',
                '.dropdown.easydropdown .selected',
                '.easydropdown .selected'
            ]

            dropdown_found = False
            for selector in dropdown_click_selectors:
                try:
                    dropdown_element = await page.query_selector(selector)
                    if dropdown_element:
                        is_visible = await dropdown_element.is_visible()
                        if is_visible:
                            await dropdown_element.click()
                            logger.info(f"Clicked dropdown using selector: {selector}")
                            dropdown_found = True
                            break
                except Exception as e:
                    logger.debug(f"Dropdown selector {selector} failed: {e}")
                    continue

            if not dropdown_found:
                logger.warning("Could not find dropdown to apply filter")
                return False

            # Wait for dropdown to open
            await asyncio.sleep(2)

            # Find and click the "e-Facturi cu eroare" option
            option_selectors = [
                '.einvoice-status-restricted .easydropdown ul li:has-text("e-Facturi cu eroare")',
                '.easydropdown ul li:has-text("e-Facturi cu eroare")',
                'li:has-text("e-Facturi cu eroare")'
            ]

            option_found = False
            for selector in option_selectors:
                try:
                    option_element = await page.query_selector(selector)
                    if option_element:
                        is_visible = await option_element.is_visible()
                        if is_visible:
                            await option_element.click()
                            logger.info(f"Clicked 'e-Facturi cu eroare' option using selector: {selector}")
                            option_found = True
                            break
                except Exception as e:
                    logger.debug(f"Option selector {selector} failed: {e}")
                    continue

            if not option_found:
                logger.warning("Could not find 'e-Facturi cu eroare' option")
                return False

            # Wait for filter to be applied
            await asyncio.sleep(3)
            logger.info("Filter applied successfully")
            return True

        except Exception as e:
            logger.error(f"Error applying filter manually: {e}")
            return False

    async def _get_fresh_invoices_page_reference(self):
        """
        Get a fresh, valid reference to the invoices page.

        Returns:
            Page: Valid page reference to invoices page, or None if not found
        """
        try:
            logger.info("Getting fresh invoices page reference...")

            # Get all available pages
            pages = self.context.pages
            logger.info(f"Found {len(pages)} total pages")

            # Look for the invoices page
            for i, page in enumerate(pages):
                try:
                    page_url = page.url
                    logger.info(f"Page {i+1}: {page_url}")

                    if 'raport/facturi' in page_url:
                        logger.info(f"Found invoices page at index {i+1}")
                        # Test if the page is still valid
                        await page.evaluate('document.readyState')
                        self.page = page
                        return page
                except Exception as e:
                    logger.debug(f"Page {i+1} is invalid: {e}")
                    continue

            # If no invoices page found, use the first valid page and navigate to invoices
            for i, page in enumerate(pages):
                try:
                    await page.evaluate('document.readyState')
                    logger.info(f"Using page {i+1} and navigating to invoices")
                    await page.goto("https://cloud.smartbill.ro/raport/facturi/", timeout=60000)
                    await page.wait_for_load_state('domcontentloaded', timeout=60000)

                    # Re-apply filter if needed
                    current_url = page.url
                    if not self._check_if_filter_already_applied_via_url(current_url):
                        logger.info("Applying e-Invoice error filter after navigation...")
                        await self._apply_einvoice_error_filter_manually(page)

                    self.page = page
                    return page
                except Exception as e:
                    logger.debug(f"Could not use page {i+1}: {e}")
                    continue

            logger.error("No valid pages found")
            return None

        except Exception as e:
            logger.error(f"Error getting fresh page reference: {e}")
            return None

    async def _ensure_valid_invoices_page(self):
        """
        Ensure we have a valid, functional invoices page reference.

        Returns:
            Page: Valid page reference to invoices page, or None if failed
        """
        try:
            logger.info("Ensuring valid invoices page reference...")

            # First try to get a fresh page reference
            main_tab = await self._get_fresh_invoices_page_reference()
            if not main_tab:
                logger.error("Could not get fresh page reference")
                return None

            # Verify the page is functional
            try:
                current_url = main_tab.url
                logger.info(f"Current URL: {current_url}")

                if 'raport/facturi' not in current_url:
                    logger.warning("Not on invoices page, navigating...")
                    await main_tab.goto("https://cloud.smartbill.ro/raport/facturi/", timeout=60000)
                    await main_tab.wait_for_load_state('domcontentloaded', timeout=60000)
                    await asyncio.sleep(3)

                    # Re-apply filter if needed
                    current_url_after_nav = main_tab.url
                    if not self._check_if_filter_already_applied_via_url(current_url_after_nav):
                        logger.info("Applying e-Invoice error filter after navigation...")
                        await self._apply_einvoice_error_filter_manually(main_tab)

                return main_tab

            except Exception as e:
                logger.error(f"Page verification failed: {e}")
                return None

        except Exception as e:
            logger.error(f"Error ensuring valid invoices page: {e}")
            return None

    async def _wait_for_invoices_table_ready(self, page) -> bool:
        """
        Wait for the invoices table to be fully loaded and ready for interaction.

        Args:
            page: The page to check

        Returns:
            bool: True if table is ready, False if timeout or error
        """
        try:
            logger.info("Waiting for invoices table to be ready...")

            # Wait for table to exist
            await page.wait_for_selector('#invoices_datatable tbody tr', timeout=30000)
            logger.info("Table rows found")

            # Wait for any loading indicators to disappear
            await asyncio.sleep(2)

            # Check if we have actual invoice rows
            invoice_rows = await page.query_selector_all('#invoices_datatable tbody tr.lvl_one_row')
            logger.info(f"Found {len(invoice_rows)} invoice rows")

            if len(invoice_rows) == 0:
                logger.warning("No invoice rows found in table")
                return False

            # Check if dropdown elements are present
            dropdown_elements = await page.query_selector_all('.actiuni_unelte')
            logger.info(f"Found {len(dropdown_elements)} dropdown elements")

            return True

        except Exception as e:
            logger.error(f"Error waiting for invoices table: {e}")
            return False

    async def navigate_to_einvoice_errors(self) -> bool:
        """
        Navigate to the e-Invoice errors section and apply filter.
        Skips manual navigation if e-Invoice notification modal was already handled.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            # Check if e-Invoice modal was already handled during login
            if self.einvoice_modal_handled:
                logger.info("E-Invoice notification modal was already handled - skipping manual navigation")

                # Verify we're on the correct page with filter applied
                current_url = self.page.url
                if 'raport/facturi' in current_url or 'facturi' in current_url:
                    logger.info("Already on invoices page via modal - checking if filter is already applied")

                    # Wait for page to fully load
                    await asyncio.sleep(3)

                    # Check URL parameters first - most reliable indicator
                    filter_already_applied = self._check_if_filter_already_applied_via_url(current_url)

                    # Also check if we detected the filter during page loading
                    if hasattr(self, 'filter_already_applied_via_modal') and self.filter_already_applied_via_modal:
                        logger.info("Filter confirmed as applied via modal during page loading - skipping manual filter application")
                        return True
                    elif filter_already_applied:
                        # Check if it's the no-survey parameter specifically
                        if 'no-survey=true' in current_url.lower():
                            logger.info("Filter already applied via modal (no-survey parameter detected) - skipping manual filter application")
                        else:
                            logger.info("Filter already applied via modal - skipping manual filter application")
                        return True
                    else:
                        logger.info("Filter not detected in URL - will apply manually")
                        # Continue to apply filter manually
                else:
                    logger.warning("Modal was handled but not on expected page - proceeding with full navigation")
                    self.einvoice_modal_handled = False  # Reset flag

            logger.info("Navigating to e-Invoice errors section")

            # Check if we're already on the invoices page (from modal handling)
            current_url = self.page.url
            if not ('raport/facturi' in current_url or 'facturi' in current_url):
                # Navigate directly to the invoices report page
                logger.info("Loading invoices page...")
                await self.page.goto("https://cloud.smartbill.ro/raport/facturi/", timeout=60000)  # Increased timeout

                # Wait for page to load with longer timeout for slow internet
                try:
                    await self.page.wait_for_load_state('domcontentloaded', timeout=60000)
                    logger.info("Invoices page loaded")
                except Exception as e:
                    logger.warning(f"Page load timeout, continuing anyway: {e}")
            else:
                logger.info("Already on invoices page - skipping navigation")

            # Wait for the page to fully load
            await asyncio.sleep(3)

            # Check if filter is already applied (in case of manual navigation with parameters)
            current_url = self.page.url
            if self._check_if_filter_already_applied_via_url(current_url):
                if 'no-survey=true' in current_url.lower():
                    logger.info("Filter already applied via URL parameters (no-survey parameter detected) - skipping manual filter application")
                else:
                    logger.info("Filter already applied via URL parameters - skipping manual filter application")
                return True

            # Apply e-Invoice error filter manually
            logger.info("Applying e-Invoice error filter manually using dropdown...")

            try:
                # Click the dropdown to open it
                dropdown_click_selectors = [
                    '.einvoice-status-restricted .dropdown.easydropdown .selected',  # The "Toate" span
                    '.einvoice-status-restricted .dropdown.easydropdown',  # The dropdown container
                    '.dropdown.easydropdown .selected',  # Alternative
                    '.easydropdown .selected'  # More general
                ]

                dropdown_found = False
                for selector in dropdown_click_selectors:
                    try:
                        dropdown_element = await self.page.query_selector(selector)
                        if dropdown_element:
                            await dropdown_element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)

                            is_visible = await dropdown_element.is_visible()
                            if is_visible:
                                logger.info(f"Found dropdown element using selector: {selector}")

                                # Click to open the dropdown
                                await dropdown_element.click()
                                logger.info("Clicked dropdown to open it")
                                await asyncio.sleep(1)  # Wait for dropdown to open

                                # Now look for the "e-Facturi cu eroare" option
                                option_selectors = [
                                    '.einvoice-status-restricted .easydropdown ul li:has-text("e-Facturi cu eroare")',
                                    '.dropdown.easydropdown ul li:has-text("e-Facturi cu eroare")',
                                    '.easydropdown ul li:has-text("e-Facturi cu eroare")',
                                    'li:has-text("e-Facturi cu eroare")'
                                ]

                                for option_selector in option_selectors:
                                    try:
                                        option = await self.page.wait_for_selector(option_selector, timeout=5000)
                                        if option:
                                            is_option_visible = await option.is_visible()
                                            if is_option_visible:
                                                logger.info(f"Found option using selector: {option_selector}")
                                                await option.click()
                                                logger.info("Successfully clicked 'e-Facturi cu eroare' option")
                                                dropdown_found = True
                                                break
                                    except Exception as e:
                                        logger.debug(f"Option selector {option_selector} failed: {e}")
                                        continue

                                if dropdown_found:
                                    break
                    except Exception as e:
                        logger.debug(f"Dropdown click selector {selector} failed: {e}")
                        continue

                if not dropdown_found:
                    logger.warning("Could not apply e-Invoice error filter")

            except Exception as e:
                logger.warning(f"Dropdown filter method failed: {e}")

            # Wait for filter to apply and page to update
            await asyncio.sleep(5)  # Give time for the filter to apply and page to update

            logger.info("Successfully navigated to e-Invoice errors section")
            return True

        except Exception as e:
            logger.error(f"Failed to navigate to e-Invoice errors: {e}")
            return False

    async def fetch_einvoice_errors(self) -> List[AddressRecord]:
        """
        Fetch e-Invoice error records from the filtered table.

        Returns:
            List[AddressRecord]: List of records with e-Invoice errors
        """
        try:
            logger.info("Fetching e-Invoice error records")

            # Wait for the table to load
            await asyncio.sleep(3)

            # Look for invoice table rows
            table_selectors = [
                '#invoices_datatable tbody tr.lvl_one_row',
                '#invoices_datatable tbody tr',
                '.datatable tbody tr',
                'table tbody tr'
            ]

            rows = []
            for selector in table_selectors:
                try:
                    rows = await self.page.query_selector_all(selector)
                    if rows:
                        logger.info(f"Found {len(rows)} invoice records using selector: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Table selector {selector} failed: {e}")
                    continue

            if not rows:
                logger.info("No e-Invoice errors found")
                return []

            records = []
            for i, row in enumerate(rows):
                try:
                    # Extract basic information from the row
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 3:
                        # Get all cell texts for analysis
                        cell_texts = []
                        for cell in cells:
                            text = await cell.inner_text()
                            cell_texts.append(text.strip())

                        # Try to find client name and invoice ID
                        client_name = "Unknown Client"
                        invoice_id = f"invoice_{i+1}"

                        # Look for invoice ID (usually a long number)
                        for text in cell_texts:
                            if text.isdigit() and len(text) >= 6:
                                invoice_id = text
                                break

                        # Look for client name (usually contains letters, not just numbers)
                        for text in cell_texts:
                            if text and not text.isdigit() and len(text) > 2 and "€" not in text and "RON" not in text:
                                # Skip dates and amounts
                                if not any(char in text for char in ['/', '.', '-']) or len(text) > 10:
                                    client_name = text
                                    break

                        # Create record
                        record = AddressRecord(
                            client_id=client_name,
                            client_name=client_name,
                            invoice_id=invoice_id,
                            current_address="",  # Will be filled when editing
                            current_city="",
                            current_county=""
                        )
                        records.append(record)
                        logger.debug(f"Created record: {client_name} (Invoice: {invoice_id})")

                except Exception as e:
                    logger.debug(f"Error processing row {i}: {e}")
                    continue

            logger.info(f"Found {len(records)} e-Invoice error records")
            return records

        except Exception as e:
            logger.error(f"Failed to fetch e-Invoice error records: {e}")
            return []

    async def update_invoice_address(self, record: AddressRecord) -> bool:
        """
        Update client address by opening the invoice edit page in a new tab.
        This method finds the dropdown in the table row and clicks "Modifica".

        Args:
            record: AddressRecord with invoice information

        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            logger.info(f"=== STARTING INVOICE PROCESSING ===")
            logger.info(f"Client ID: {record.client_id}")
            logger.info(f"Invoice ID: {record.invoice_id}")
            logger.info(f"Current Address: {record.current_address}")
            logger.info(f"Current City: {record.current_city}")
            logger.info(f"Current County: {record.current_county}")

            # === LOOP PREVENTION CHECK ===
            invoice_key = f"{record.client_id}_{record.invoice_id}"
            if invoice_key in self.processed_invoices:
                logger.warning(f"LOOP DETECTED: Invoice {invoice_key} already processed - skipping")
                return True  # Return success to avoid infinite retries

            # Mark this invoice as being processed
            self.processed_invoices.add(invoice_key)
            logger.info(f"Marked invoice {invoice_key} as being processed")

            # === COMPREHENSIVE PAGE STATE VERIFICATION ===
            logger.info("=== VERIFYING PAGE STATE BEFORE PROCESSING ===")
            page_state_ok = await self._verify_and_reset_page_state()
            if not page_state_ok:
                logger.error("Failed to verify/reset page state - aborting this invoice")
                return False

            # Step 1: Ensure we have a valid invoices page
            logger.info("=== STEP 1: PREPARING INVOICES PAGE ===")
            main_tab = await self._ensure_valid_invoices_page()
            if not main_tab:
                logger.error("FAILED: Could not get valid invoices page reference")
                return False

            logger.info("SUCCESS: Valid invoices page reference obtained")

            # Step 2: Wait for invoices table to be ready
            logger.info("=== STEP 2: WAITING FOR INVOICES TABLE ===")
            table_ready = await self._wait_for_invoices_table_ready(main_tab)
            if not table_ready:
                logger.error("✗ FAILED: Invoices table not ready")
                return False

            logger.info("SUCCESS: Invoices table is ready")

            # Step 2.5: Ensure clean table state for sequential processing
            logger.info("=== ENSURING CLEAN TABLE STATE ===")
            try:
                # Wait for table to be fully loaded
                await main_tab.wait_for_selector('#invoices_datatable tbody tr', timeout=30000)

                # Close any open dropdowns from previous processing
                logger.info("Closing any open dropdowns...")
                try:
                    # Click on table header to close dropdowns
                    table_header = await main_tab.query_selector('#invoices_datatable thead')
                    if table_header:
                        await table_header.click()
                        await asyncio.sleep(1)

                    # Press Escape to close any open elements
                    await main_tab.keyboard.press('Escape')
                    await asyncio.sleep(1)

                    logger.info("Table state cleaned")

                except Exception as clean_e:
                    logger.debug(f"Table cleanup error: {clean_e}")

                # Additional wait for dynamic content and state stabilization
                await asyncio.sleep(3)

            except Exception as e:
                logger.warning(f"Timeout waiting for invoices table: {e}")

            # Find the dropdown menu for this specific invoice
            # Try to find the dropdown in the table row
            dropdown_selectors = [
                '.actiuni_unelte',  # First available dropdown
                'li.actiuni_unelte',  # More specific
                '.unelte_ico',  # Generic fallback
                'li.unelte_ico',
                'td .actiuni_unelte',  # In table cell
                'tr .actiuni_unelte',  # In table row
                '.lvl_one_row .actiuni_unelte'  # In specific row class
            ]

            dropdown_found = False

            # === FIND SPECIFIC INVOICE ROW ===
            # Instead of always clicking the first dropdown, find the specific invoice
            logger.info(f"=== SEARCHING FOR SPECIFIC INVOICE ===")
            logger.info(f"Looking for client: {record.client_id}")
            logger.info(f"Looking for invoice ID: {record.invoice_id}")

            try:
                invoice_rows = await main_tab.query_selector_all('#invoices_datatable tbody tr.lvl_one_row')
                logger.info(f"Found {len(invoice_rows)} invoice rows in table")

                target_row = None
                target_row_index = -1

                # Search through each row to find the matching invoice
                for i, row in enumerate(invoice_rows):
                    try:
                        # Get all text content from the row
                        row_text = await row.text_content()
                        # Clean up the text for better logging
                        clean_row_text = ' '.join(row_text.split())
                        logger.info(f"Row {i+1} content: {clean_row_text[:150]}...")

                        # Enhanced matching logic with detailed verification
                        row_match = False
                        match_reason = ""

                        # Method 1: Check for client ID match
                        if record.client_id and record.client_id.strip():
                            if record.client_id in row_text:
                                logger.info(f"  ✓ Match found by client ID: {record.client_id}")
                                row_match = True
                                match_reason = f"client_id:{record.client_id}"

                        # Method 2: Check for invoice ID match (if available and not generic)
                        if not row_match and record.invoice_id and record.invoice_id.strip():
                            # Skip generic invoice IDs like "invoice_1", "invoice_2"
                            if not record.invoice_id.startswith('invoice_'):
                                if record.invoice_id in row_text:
                                    logger.info(f"  ✓ Match found by invoice ID: {record.invoice_id}")
                                    row_match = True
                                    match_reason = f"invoice_id:{record.invoice_id}"

                        # Method 3: For generic invoice IDs, use position-based matching
                        if not row_match and record.invoice_id and record.invoice_id.startswith('invoice_'):
                            try:
                                # Extract the number from "invoice_X"
                                invoice_num = int(record.invoice_id.split('_')[1])
                                # Use 1-based indexing (invoice_1 = row 0, invoice_2 = row 1, etc.)
                                if i == (invoice_num - 1):
                                    logger.info(f"  ✓ Match found by position: {record.invoice_id} -> row {i+1}")
                                    row_match = True
                                    match_reason = f"position:{record.invoice_id}->row{i+1}"
                            except (ValueError, IndexError):
                                logger.debug(f"Could not parse invoice number from: {record.invoice_id}")

                        if row_match:
                            target_row = row
                            target_row_index = i
                            logger.info(f"SUCCESS: Found target invoice in row {i+1} (reason: {match_reason})")

                            # Additional verification - get row details for confirmation
                            try:
                                # Extract key identifiers from the row for verification
                                row_cells = await row.query_selector_all('td')
                                if row_cells:
                                    cell_texts = []
                                    for cell in row_cells[:5]:  # First 5 cells should contain key info
                                        cell_text = await cell.text_content()
                                        if cell_text and cell_text.strip():
                                            cell_texts.append(cell_text.strip())

                                    logger.info(f"Row {i+1} verification - Key cells: {cell_texts}")

                            except Exception as verify_e:
                                logger.debug(f"Row verification error: {verify_e}")

                            break

                    except Exception as e:
                        logger.debug(f"Error checking row {i+1}: {e}")
                        continue

                if target_row is None:
                    logger.error(f"CRITICAL: Could not find invoice for {record.client_id} ({record.invoice_id})")
                    logger.error("This indicates a sequential processing failure")

                    # Log all available rows for debugging
                    logger.info("Available rows in table:")
                    for i, row in enumerate(invoice_rows):
                        try:
                            row_text = await row.text_content()
                            clean_text = ' '.join(row_text.split())[:100]
                            logger.info(f"  Row {i+1}: {clean_text}...")
                        except Exception:
                            logger.info(f"  Row {i+1}: [Error reading content]")

                    return False

                if target_row is None:
                    logger.error("No invoice rows found in table")
                    return False

                logger.info(f"✅ CONFIRMED: Using invoice row {target_row_index + 1} for {record.client_id}")

                # Double-verify the selected row contains our target
                try:
                    verification_text = await target_row.text_content()
                    if record.client_id in verification_text:
                        logger.info(f"✓ Row verification passed: {record.client_id} found in selected row")
                    else:
                        logger.warning(f"⚠ Row verification failed: {record.client_id} NOT found in selected row")
                        logger.warning(f"Selected row content: {verification_text[:150]}...")
                except Exception as verify_e:
                    logger.debug(f"Row verification error: {verify_e}")

            except Exception as e:
                logger.warning(f"Could not analyze invoice rows: {e}")
                return False

            # === FIND DROPDOWN IN SPECIFIC ROW ===
            logger.info(f"=== SEARCHING FOR DROPDOWN IN ROW {target_row_index + 1} ===")

            for i, selector in enumerate(dropdown_selectors):
                try:
                    logger.info(f"Trying dropdown selector {i+1}/{len(dropdown_selectors)}: {selector}")

                    # Look for dropdown specifically in the target row
                    dropdown_element = await target_row.query_selector(selector)

                    if dropdown_element:
                        logger.info(f"  ✓ Found dropdown element in target row")

                        # Scroll element into view first (with safeguards)
                        logger.info(f"  Scrolling dropdown into view...")
                        try:
                            await dropdown_element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)
                            logger.info(f"  ✓ Scroll completed")
                        except Exception as scroll_e:
                            logger.warning(f"  Scroll failed: {scroll_e}")
                            # Continue anyway - element might still be clickable

                        # Comprehensive element state check
                        is_visible = await dropdown_element.is_visible()
                        is_enabled = await dropdown_element.is_enabled()
                        bounding_box = await dropdown_element.bounding_box()

                        logger.info(f"  Dropdown state - Visible: {is_visible}, Enabled: {is_enabled}")
                        logger.info(f"  Bounding box: {bounding_box}")

                        if is_visible and is_enabled and bounding_box:
                            # Verify this is the correct row by checking nearby content
                            try:
                                parent_row = await dropdown_element.evaluate('el => el.closest("tr")')
                                if parent_row:
                                    logger.info(f"  ✓ Dropdown is in a table row")
                            except Exception:
                                pass

                            # Click to show the dropdown
                            logger.info(f"  Attempting to click dropdown...")
                            await dropdown_element.click()
                            dropdown_found = True
                            logger.info(f"SUCCESS: Clicked dropdown for row {target_row_index + 1}")

                            # Wait and verify dropdown opened
                            await asyncio.sleep(2)

                            # === CRITICAL: FIND THE SPECIFIC OPENED DROPDOWN ===
                            # Store reference to the opened dropdown for scoped edit link search
                            opened_dropdown = None
                            try:
                                # Look for dropdown menus near the clicked element
                                dropdown_candidates = await main_tab.query_selector_all('.dropDown, ul.dropDown, .dropdown-menu')

                                for candidate in dropdown_candidates:
                                    if await candidate.is_visible():
                                        # Check if this dropdown is associated with our target row
                                        try:
                                            # Get the dropdown's position relative to our target row
                                            dropdown_box = await candidate.bounding_box()
                                            row_box = await target_row.bounding_box()

                                            if dropdown_box and row_box:
                                                # Check if dropdown is positioned near our target row
                                                vertical_distance = abs(dropdown_box['y'] - row_box['y'])
                                                if vertical_distance < 100:  # Within reasonable distance
                                                    opened_dropdown = candidate
                                                    logger.info(f"  ✓ Found opened dropdown associated with target row")
                                                    break
                                        except Exception:
                                            continue

                                if not opened_dropdown:
                                    # Fallback: use the first visible dropdown
                                    for candidate in dropdown_candidates:
                                        if await candidate.is_visible():
                                            opened_dropdown = candidate
                                            logger.warning(f"  ⚠ Using fallback dropdown (first visible)")
                                            break

                                if opened_dropdown:
                                    logger.info(f"  ✅ Dropdown menu opened and referenced successfully")
                                else:
                                    logger.error(f"  ❌ Could not find opened dropdown menu")

                            except Exception as dropdown_ref_e:
                                logger.warning(f"  Dropdown reference error: {dropdown_ref_e}")

                            break
                        else:
                            logger.info(f"  ✗ Dropdown not clickable - Visible: {is_visible}, Enabled: {is_enabled}, HasBox: {bool(bounding_box)}")
                    else:
                        logger.info(f"  ✗ No dropdown element found with selector: {selector}")

                except Exception as e:
                    logger.debug(f"  ✗ Dropdown selector {selector} failed for target row: {e}")
                    continue

            if not dropdown_found:
                logger.error(f"Could not find dropdown menu")
                # Log page state for debugging
                try:
                    page_title = await main_tab.title()
                    page_url = main_tab.url
                    logger.error(f"Page state - Title: '{page_title}', URL: '{page_url}'")
                except Exception:
                    pass
                return False

            # === SCOPED EDIT LINK SEARCH ===
            # Search for edit link ONLY within the specific opened dropdown
            logger.info("=== SEARCHING FOR EDIT LINK IN SPECIFIC DROPDOWN ===")

            edit_link = None

            # Check if we have a reference to the opened dropdown
            if 'opened_dropdown' in locals() and opened_dropdown:
                logger.info(f"Searching for edit link within the specific opened dropdown...")

                # Edit link selectors for scoped search within the dropdown
                scoped_edit_selectors = [
                    'a:has-text("Modifica")',  # Direct text match
                    'a[href*="/documente/editare/factura/"]',  # Edit URL pattern
                    'a[title*="Modifica"]',  # Title attribute
                    'a[title*="Edit"]',  # English title
                    'a'  # Any link as fallback
                ]

                for i, selector in enumerate(scoped_edit_selectors):
                    logger.info(f"  Trying scoped selector {i+1}/{len(scoped_edit_selectors)}: {selector}")
                    try:
                        # CRITICAL: Search within the specific dropdown, not the entire page
                        edit_links = await opened_dropdown.query_selector_all(selector)

                        for link in edit_links:
                            if await link.is_visible():
                                # Verify this is actually an edit link
                                href = await link.get_attribute('href')
                                text = await link.text_content()
                                title = await link.get_attribute('title')

                                logger.info(f"    Found link - href: '{href}', text: '{text}', title: '{title}'")

                                # Verify it's an edit link
                                if (href and '/documente/editare/factura/' in href) or \
                                   (text and 'Modifica' in text) or \
                                   (title and ('Modifica' in title or 'Edit' in title)):

                                    edit_link = link
                                    logger.info(f"  ✅ SUCCESS: Found valid edit link in specific dropdown")
                                    logger.info(f"    Link details - href: '{href}', text: '{text}'")

                                    # Additional verification: check if this edit link is for our target invoice
                                    if href and record.client_id:
                                        logger.info(f"    Verifying edit link is for target client: {record.client_id}")
                                        # The href should contain invoice ID or we should verify it's the right one
                                        logger.info(f"    Edit URL: {href}")

                                    break

                        if edit_link:
                            break

                    except Exception as e:
                        logger.debug(f"    Scoped selector {selector} failed: {e}")
                        continue

                if edit_link:
                    logger.info(f"✅ Found edit link using scoped search within specific dropdown")
                else:
                    logger.warning(f"⚠ No edit link found in specific dropdown - trying fallback")

            # Fallback: search entire page if scoped search failed
            if not edit_link:
                logger.warning(f"Falling back to page-wide edit link search...")
                fallback_selectors = [
                    'a:has-text("Modifica")',
                    '.dropDown a:has-text("Modifica")',
                    'ul.dropDown a:has-text("Modifica")',
                    'a[href*="/documente/editare/factura/"]'
                ]

                for selector in fallback_selectors:
                    try:
                        edit_link = await main_tab.query_selector(selector)
                        if edit_link and await edit_link.is_visible():
                            logger.warning(f"Found edit link using fallback selector: {selector}")
                            break
                    except Exception as e:
                        logger.debug(f"Fallback selector {selector} failed: {e}")
                        continue

            if not edit_link:
                logger.error(f"❌ CRITICAL: Could not find edit link for {record.client_id}")
                logger.error(f"This indicates a dropdown targeting failure")
                return False

            # === FINAL VERIFICATION OF EDIT LINK ===
            logger.info("=== VERIFYING EDIT LINK BEFORE CLICKING ===")

            # Get the edit URL and verify it's correct
            edit_url = await edit_link.get_attribute('href')
            edit_text = await edit_link.text_content()
            edit_title = await edit_link.get_attribute('title')

            logger.info(f"Edit link details:")
            logger.info(f"  URL: {edit_url}")
            logger.info(f"  Text: '{edit_text}'")
            logger.info(f"  Title: '{edit_title}'")
            logger.info(f"  Target client: {record.client_id}")

            # Verify this is actually an edit link
            if not edit_url or '/documente/editare/factura/' not in edit_url:
                logger.error(f"❌ Invalid edit URL: {edit_url}")
                return False

            # Make URL absolute if needed
            if edit_url and not edit_url.startswith('http'):
                edit_url = f"https://cloud.smartbill.ro{edit_url}"

            logger.info(f"✅ VERIFIED: Opening edit page for {record.client_id}: {edit_url}")

            # Open edit page in new tab with increased timeout and retry logic
            edit_tab = await self.context.new_page()

            # Try loading the page with retries
            page_loaded = False
            max_load_attempts = 3

            for attempt in range(1, max_load_attempts + 1):
                try:
                    logger.info(f"Loading edit page (attempt {attempt}/{max_load_attempts}) with 90s timeout...")

                    # Step 1: Navigate to page with extended timeout
                    logger.info(f"Navigating to: {edit_url}")
                    await edit_tab.goto(edit_url, timeout=90000, wait_until='commit')
                    logger.info(f"Navigation committed successfully")

                    # Step 2: Simple approach - just wait for basic DOM content
                    try:
                        await edit_tab.wait_for_load_state('domcontentloaded', timeout=30000)
                        logger.info(f"DOM content loaded")
                    except Exception as dom_e:
                        logger.warning(f"DOM load timeout, but continuing: {dom_e}")

                    # Step 3: Verify page is actually loaded by checking for key elements
                    logger.info(f"Verifying page content is loaded...")

                    # Wait for any of these key elements that indicate the edit page is loaded
                    key_elements = [
                        '#client_name',  # Client name field
                        '#client_details_span',  # Client edit button container
                        '.issuing-client-edit',  # Client edit button class
                        'input[name="client_name"]',  # Alternative client field
                        'form',  # Any form on the page
                        '.form-control'  # Form control elements
                    ]

                    element_found = False
                    for selector in key_elements:
                        try:
                            element = await edit_tab.wait_for_selector(selector, timeout=10000)
                            if element:
                                logger.info(f"Found key element: {selector}")
                                element_found = True
                                break
                        except Exception:
                            continue

                    if element_found:
                        page_loaded = True
                        logger.info(f"Edit page verified as loaded on attempt {attempt}")
                        break
                    else:
                        # Fallback: Check if we're on the right URL
                        current_url = edit_tab.url
                        if 'editare/factura' in current_url:
                            logger.info(f"Page loaded (verified by URL): {current_url}")
                            page_loaded = True
                            break
                        else:
                            raise Exception(f"Page content verification failed - no key elements found")

                except Exception as load_e:
                    logger.warning(f"❌ Page load attempt {attempt} failed: {load_e}")
                    logger.warning(f"❌ Error type: {type(load_e).__name__}")

                    # Check if page might actually be loaded despite the error
                    try:
                        current_url = edit_tab.url
                        logger.info(f"🔍 Current URL after error: {current_url}")

                        if 'editare/factura' in current_url:
                            logger.info(f"🎯 URL suggests page loaded despite error - attempting to continue")
                            page_loaded = True
                            break
                    except Exception:
                        pass

                    if attempt < max_load_attempts:
                        logger.info(f"⏳ Retrying page load in 5 seconds...")
                        await asyncio.sleep(5)
                    else:
                        logger.error(f"❌ All page load attempts failed")
                        await edit_tab.close()
                        await main_tab.bring_to_front()
                        self.page = main_tab
                        return False

            if not page_loaded:
                logger.error("❌ Failed to load edit page after all attempts")
                await edit_tab.close()
                await main_tab.bring_to_front()
                self.page = main_tab
                return False

            # Final verification: Ensure page is interactive
            logger.info("Final verification - checking if page is interactive...")
            try:
                # Check page title
                page_title = await edit_tab.title()
                logger.info(f"Page title: {page_title}")

                # Check if we can interact with the page
                ready_state = await edit_tab.evaluate('document.readyState')
                logger.info(f"Document ready state: {ready_state}")

                # Verify URL one more time
                final_url = edit_tab.url
                logger.info(f"Final URL: {final_url}")

                if 'editare/factura' not in final_url:
                    logger.warning(f"URL doesn't contain expected path, but continuing anyway")

                logger.info("Page verification complete - proceeding to client edit detection")

            except Exception as verify_e:
                logger.warning(f"Page verification failed, but continuing anyway: {verify_e}")

            # Now we're on the edit invoice page - need to click "Edit Client"
            logger.info("Looking for client edit button...")
            logger.info("*** USING ENHANCED DEBUGGING VERSION ***")

            # Give the page extra time to fully load all JavaScript and elements
            logger.info("Waiting for page to fully stabilize...")
            await asyncio.sleep(5)

            # First, let's do comprehensive page analysis
            logger.info("=== PAGE ANALYSIS START ===")

            # Check page loading state
            ready_state = await edit_tab.evaluate('document.readyState')
            logger.info(f"Document ready state: {ready_state}")

            # Check for loading indicators
            loading_indicators = await edit_tab.query_selector_all('.loading, .spinner, [class*="load"]')
            logger.info(f"Found {len(loading_indicators)} potential loading indicators")

            # Wait for page to be fully loaded
            logger.info("Waiting for page to fully load...")
            await asyncio.sleep(5)  # Longer wait

            # Check if client name field exists and its state
            client_field = await edit_tab.query_selector('#client_name')
            if client_field:
                is_disabled = await client_field.get_attribute('disabled')
                field_value = await client_field.input_value()
                logger.info(f"Client field found - disabled: {is_disabled}, value: '{field_value}'")
            else:
                logger.warning("Client name field (#client_name) not found!")

            # Check if the span container exists
            span_container = await edit_tab.query_selector('#client_details_span')
            if span_container:
                span_classes = await span_container.get_attribute('class')
                span_style = await span_container.get_attribute('style')
                span_display = await span_container.evaluate('el => window.getComputedStyle(el).display')
                logger.info(f"Span container found - classes: '{span_classes}', style: '{span_style}', computed display: '{span_display}'")
            else:
                logger.warning("Span container (#client_details_span) not found!")

            # Look for ALL elements that might be the edit button
            all_edit_elements = await edit_tab.query_selector_all('a[href*="edit_client"], [data-original-title*="client"], [title*="client"], .edit, [class*="edit"]')
            logger.info(f"Found {len(all_edit_elements)} potential edit elements on page")

            for i, elem in enumerate(all_edit_elements):
                try:
                    tag_name = await elem.evaluate('el => el.tagName')
                    href = await elem.get_attribute('href')
                    title = await elem.get_attribute('data-original-title') or await elem.get_attribute('title')
                    classes = await elem.get_attribute('class')
                    is_visible = await elem.is_visible()
                    logger.info(f"  Element {i+1}: {tag_name}, href='{href}', title='{title}', classes='{classes}', visible={is_visible}")
                except Exception as e:
                    logger.debug(f"  Element {i+1}: Error analyzing - {e}")

            logger.info("=== PAGE ANALYSIS END ===")

            # Look for client edit button - based on your exact HTML
            client_edit_selectors = [
                '#client_details_span a[href="javascript:edit_client();"]',  # Exact match from your HTML
                'span.issuing-client-edit a[href="javascript:edit_client();"]',  # With span class
                'a[data-original-title="Modifica client"]',  # By tooltip text
                'a[href="javascript:edit_client();"]',  # By href only
                '#client_details_span a',  # Any link in the span
                '.issuing-client-edit a',  # Any link in the class
                'span#client_details_span a',  # More specific span
                'span.issuing-client-edit#client_details_span a'  # Most specific
            ]

            logger.info("=== SELECTOR TESTING START ===")
            client_edit_found = False

            for i, selector in enumerate(client_edit_selectors):
                try:
                    logger.info(f"[{i+1}/{len(client_edit_selectors)}] Trying selector: {selector}")
                    client_edit_btn = await edit_tab.query_selector(selector)

                    if client_edit_btn:
                        logger.info(f"Element FOUND with selector: {selector}")

                        # Get detailed element info
                        try:
                            tag_name = await client_edit_btn.evaluate('el => el.tagName')
                            href = await client_edit_btn.get_attribute('href')
                            classes = await client_edit_btn.get_attribute('class')
                            display_style = await client_edit_btn.evaluate('el => window.getComputedStyle(el).display')
                            visibility = await client_edit_btn.evaluate('el => window.getComputedStyle(el).visibility')
                            opacity = await client_edit_btn.evaluate('el => window.getComputedStyle(el).opacity')

                            logger.info(f"  Tag: {tag_name}, href: '{href}', classes: '{classes}'")
                            logger.info(f"  CSS - display: '{display_style}', visibility: '{visibility}', opacity: '{opacity}'")
                        except Exception as e:
                            logger.debug(f"  Error getting element details: {e}")

                        # Scroll into view first
                        logger.info("  Scrolling element into view...")
                        await client_edit_btn.scroll_into_view_if_needed()
                        await asyncio.sleep(1)

                        is_visible = await client_edit_btn.is_visible()
                        logger.info(f"  Element is_visible(): {is_visible}")

                        # Try clicking regardless of visibility check (sometimes is_visible() is unreliable)
                        logger.info(f"Attempting to click element (visible={is_visible})...")

                        # First, try a quick JavaScript click to avoid overlapping element issues
                        try:
                            logger.info("Trying JavaScript click first...")
                            await client_edit_btn.evaluate('el => el.click()')
                            client_edit_found = True
                            logger.info("Successfully clicked client edit button using JavaScript")

                            # Wait a moment for any popup/modal to appear
                            await asyncio.sleep(3)
                            break

                        except Exception as js_click_e:
                            logger.warning(f"JavaScript click failed: {js_click_e}")

                            # Check if page is still alive
                            try:
                                await edit_tab.title()  # Simple check if page is still accessible
                            except Exception:
                                logger.error("Page/browser was closed during click attempt")
                                return False

                            # Try regular click as fallback with shorter timeout
                            try:
                                logger.info("Trying regular click as fallback...")
                                await client_edit_btn.click(timeout=10000)  # Shorter timeout
                                client_edit_found = True
                                logger.info("Successfully clicked client edit button")
                                await asyncio.sleep(3)
                                break
                            except Exception as click_e:
                                logger.warning(f"Regular click also failed: {click_e}")

                                # Check if page is still alive after click attempt
                                try:
                                    await edit_tab.title()
                                except Exception:
                                    logger.error("Page/browser was closed during click attempt")
                                    return False

                        # If direct click failed and element not visible, try hover approach
                        if not is_visible:
                            logger.warning(f"✗ Element found but NOT VISIBLE with selector: {selector}")

                            # Try to make it visible by hovering over the client field
                            logger.info("  Attempting hover over client field to trigger visibility...")
                            try:
                                client_field = await edit_tab.query_selector('#client_name')
                                if client_field:
                                    logger.info("  Hovering over #client_name...")
                                    await client_field.hover()
                                    await asyncio.sleep(2)

                                    # Check visibility again
                                    is_visible_after_hover = await client_edit_btn.is_visible()
                                    logger.info(f"  Element visible after hover: {is_visible_after_hover}")

                                    if is_visible_after_hover:
                                        logger.info(f"✓ SUCCESS: Client edit button became visible after hover!")
                                        try:
                                            await client_edit_btn.click()
                                            client_edit_found = True
                                            logger.info("✓ Successfully clicked client edit button after hover")
                                            break
                                        except Exception as click_e:
                                            logger.error(f"✗ Click failed after hover: {click_e}")
                                            continue
                                    else:
                                        logger.warning("✗ Element still not visible after hover")
                                else:
                                    logger.warning("✗ Client field (#client_name) not found for hover")
                            except Exception as hover_e:
                                logger.error(f"✗ Hover attempt failed: {hover_e}")
                    else:
                        logger.warning(f"✗ Element NOT FOUND with selector: {selector}")

                except Exception as e:
                    logger.error(f"✗ Selector {selector} failed with error: {e}")
                    continue

            logger.info("=== SELECTOR TESTING END ===")

            # If still not found, try JavaScript approach
            if not client_edit_found:
                logger.info("=== TRYING JAVASCRIPT APPROACH ===")
                try:
                    # Try to call the edit_client function directly
                    logger.info("Attempting to call edit_client() function directly...")
                    result = await edit_tab.evaluate('typeof edit_client === "function" ? edit_client() : "function not found"')
                    logger.info(f"Direct function call result: {result}")
                    if result != "function not found":
                        client_edit_found = True
                        logger.info("✓ Successfully called edit_client() directly")
                except Exception as js_e:
                    logger.error(f"✗ JavaScript approach failed: {js_e}")

                logger.info("=== JAVASCRIPT APPROACH END ===")

            if not client_edit_found:
                logger.error("Could not find client edit button")
                await edit_tab.close()
                await main_tab.bring_to_front()
                self.page = main_tab
                return False

            # Wait for client edit form/popup to appear
            await asyncio.sleep(3)

            # Extract current address and county from the client edit modal
            logger.info("=== MODAL INTERACTION START ===")
            logger.info("Extracting current client address...")

            try:
                # Wait for modal to be fully loaded and visible
                logger.info("Waiting for client edit modal to fully load...")
                await asyncio.sleep(3)

                # === CRITICAL: VERIFY MODAL STATE ===
                logger.info("Verifying modal is properly opened and stable...")
                try:
                    # Check if modal is actually visible and not stuck
                    modal_visible = await edit_tab.query_selector('.modal.show, .modal.in, .modal[style*="display: block"]')
                    if not modal_visible:
                        logger.error("Modal not found or not visible - aborting modal interaction")
                        return False

                    # Verify modal is not in a loading state
                    loading_indicators = await edit_tab.query_selector_all('.loading, .spinner, [class*="loading"]')
                    for indicator in loading_indicators:
                        if await indicator.is_visible():
                            logger.warning("Modal still loading - waiting...")
                            await asyncio.sleep(2)
                            break

                    logger.info("✓ Modal state verified - proceeding with interaction")

                except Exception as modal_verify_e:
                    logger.warning(f"Modal verification failed: {modal_verify_e}")
                    # Continue anyway but with caution

                # Check if modal is visible
                modal_selectors = [
                    '.modal.show',
                    '.modal.in',
                    '.modal[style*="display: block"]',
                    '#client_modal',
                    '.client-edit-modal'
                ]

                modal_found = False
                for selector in modal_selectors:
                    try:
                        modal = await edit_tab.query_selector(selector)
                        if modal:
                            is_visible = await modal.is_visible()
                            logger.info(f"Modal found with selector {selector}, visible: {is_visible}")
                            if is_visible:
                                modal_found = True
                                break
                    except Exception as e:
                        logger.debug(f"Modal selector {selector} failed: {e}")

                if not modal_found:
                    logger.warning("No visible modal detected, but continuing with field extraction...")
                else:
                    logger.info("Client edit modal confirmed as visible and loaded")

                # Look for address fields in the client edit modal based on your HTML structure
                address_selectors = [
                    '#client_address',  # Based on your modal HTML
                    'textarea[name="address"]',
                    'textarea[id="address"]',
                    'input[name="address"]',
                    'input[id="address"]'
                ]

                city_selectors = [
                    '#client_city',  # Based on your modal HTML
                    'input[name="city"]',
                    'input[id="city"]',
                    'input[name="oras"]',
                    'input[id="oras"]'
                ]

                county_selectors = [
                    '#client_county',  # Based on your modal HTML
                    'input[name="county"]',
                    'input[id="county"]',
                    'input[name="judet"]',
                    'input[id="judet"]',
                    'select[name="county"]',
                    'select[name="judet"]'
                ]

                current_address = ""
                current_city = ""
                current_county = ""

                # === ENHANCED FIELD EXTRACTION DEBUGGING ===
                logger.info("=== FIELD EXTRACTION START ===")

                # Extract current address with detailed debugging
                logger.info("=== ADDRESS FIELD EXTRACTION ===")
                logger.info(f"Trying {len(address_selectors)} address selectors...")
                address_found = False

                for i, selector in enumerate(address_selectors):
                    logger.info(f"Address attempt {i+1}/{len(address_selectors)}: {selector}")
                    try:
                        address_field = await edit_tab.query_selector(selector)
                        if address_field:
                            logger.info(f"  Element found with selector: {selector}")
                            is_visible = await address_field.is_visible()
                            is_enabled = await address_field.is_enabled()
                            logger.info(f"  Element state - visible: {is_visible}, enabled: {is_enabled}")

                            current_address = await address_field.input_value()
                            safe_address_log = current_address.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                            logger.info(f"  Address value extracted: '{safe_address_log}'")

                            if current_address.strip():
                                logger.info(f"  SUCCESS: Non-empty address found with selector {selector}")
                                address_found = True
                                break
                            else:
                                logger.info(f"  WARNING: Address field is empty, trying next selector...")
                        else:
                            logger.info(f"  No element found with selector: {selector}")
                    except Exception as e:
                        logger.info(f"  ✗ Error with selector {selector}: {e}")
                        continue

                if not address_found:
                    logger.warning("⚠ NO ADDRESS FIELD FOUND OR ALL FIELDS EMPTY")

                # Extract current city with detailed debugging
                logger.info("=== CITY FIELD EXTRACTION ===")
                logger.info(f"Trying {len(city_selectors)} city selectors...")
                city_found = False

                for i, selector in enumerate(city_selectors):
                    logger.info(f"City attempt {i+1}/{len(city_selectors)}: {selector}")
                    try:
                        city_field = await edit_tab.query_selector(selector)
                        if city_field:
                            logger.info(f"  ✓ Element found with selector: {selector}")
                            is_visible = await city_field.is_visible()
                            is_enabled = await city_field.is_enabled()
                            logger.info(f"  ✓ Element state - visible: {is_visible}, enabled: {is_enabled}")

                            current_city = await city_field.input_value()
                            safe_city_log = current_city.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                            logger.info(f"  ✓ City value extracted: '{safe_city_log}'")

                            if current_city.strip():
                                logger.info(f"  ✓ SUCCESS: Non-empty city found with selector {selector}")
                                city_found = True
                                break
                            else:
                                logger.info(f"  ⚠ City field is empty, trying next selector...")
                        else:
                            logger.info(f"  ✗ No element found with selector: {selector}")
                    except Exception as e:
                        logger.info(f"  ✗ Error with selector {selector}: {e}")
                        continue

                if not city_found:
                    logger.warning("⚠ NO CITY FIELD FOUND OR ALL FIELDS EMPTY")

                # Extract current county with detailed debugging
                logger.info("=== COUNTY FIELD EXTRACTION ===")
                logger.info(f"Trying {len(county_selectors)} county selectors...")
                county_found = False

                for i, selector in enumerate(county_selectors):
                    logger.info(f"County attempt {i+1}/{len(county_selectors)}: {selector}")
                    try:
                        county_field = await edit_tab.query_selector(selector)
                        if county_field:
                            logger.info(f"  ✓ Element found with selector: {selector}")
                            is_visible = await county_field.is_visible()
                            is_enabled = await county_field.is_enabled()
                            logger.info(f"  ✓ Element state - visible: {is_visible}, enabled: {is_enabled}")

                            # For select elements, get selected option text
                            if selector.startswith('select'):
                                current_county = await county_field.evaluate('el => el.options[el.selectedIndex]?.text || el.value')
                                logger.info(f"  ✓ County value extracted from SELECT: '{current_county}'")
                            else:
                                current_county = await county_field.input_value()
                                logger.info(f"  ✓ County value extracted from INPUT: '{current_county}'")

                            if current_county.strip():
                                logger.info(f"  ✓ SUCCESS: Non-empty county found with selector {selector}")
                                county_found = True
                                break
                            else:
                                logger.info(f"  ⚠ County field is empty, trying next selector...")
                        else:
                            logger.info(f"  ✗ No element found with selector: {selector}")
                    except Exception as e:
                        logger.info(f"  ✗ Error with selector {selector}: {e}")
                        continue

                if not county_found:
                    logger.warning("⚠ NO COUNTY FIELD FOUND OR ALL FIELDS EMPTY")

                logger.info("=== FIELD EXTRACTION COMPLETE ===")
                logger.info(f"Final extracted values:")
                logger.info(f"  Address: '{current_address}' (length: {len(current_address)})")
                logger.info(f"  City: '{current_city}' (length: {len(current_city)})")
                logger.info(f"  County: '{current_county}' (length: {len(current_county)})")

                # Update record with current address info
                record.current_address = current_address
                record.current_city = current_city
                record.current_county = current_county

                # Sanitize Romanian characters for logging to avoid Unicode errors
                safe_address = current_address.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                safe_city = current_city.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                safe_county = current_county.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                logger.info(f"Extracted values - Address: '{safe_address}', City: '{safe_city}', County: '{safe_county}'")

                # === ENHANCED GEOCODING PROCESS DEBUGGING ===
                logger.info("=== GEOCODING PROCESS START ===")

                # Check if city is Bucuresti (case-insensitive, handle Romanian characters)
                city_normalized = current_city.lower().replace('ș', 's').replace('ş', 's').replace('ț', 't').replace('ţ', 't')
                logger.info(f"City normalization: '{current_city}' -> '{city_normalized}'")

                if city_normalized in ['bucuresti', 'bucharest'] or 'bucurest' in city_normalized:
                    logger.info(f"✓ Bucuresti city detected - proceeding with geocoding")
                    logger.info(f"Original address for geocoding: '{current_address}'")

                    if not current_address or not current_address.strip():
                        logger.warning("✗ NO ADDRESS FOUND TO GEOCODE - skipping geocoding process")
                    else:
                        logger.info("=== ADDRESS CLEANING PROCESS ===")
                        # Clean address for geocoding - stop at first comma to get only street name and number
                        address_parts = current_address.split(',')
                        cleaned_address = address_parts[0].strip()
                        logger.info(f"Address split into {len(address_parts)} parts: {address_parts}")
                        logger.info(f"Cleaned address (first part): '{cleaned_address}'")
                        logger.info(f"Removed parts: {address_parts[1:] if len(address_parts) > 1 else 'None'}")

                        # Import geocoding service here to avoid circular imports
                        try:
                            logger.info("=== GOOGLE MAPS API CALL ===")
                            from .geocoding_service import GeocodingService
                            geocoding = GeocodingService(self.config)

                            # Prepare geocoding parameters
                            geocoding_address = cleaned_address
                            geocoding_city = current_city
                            geocoding_county = current_county or "Bucuresti"

                            logger.info(f"Geocoding parameters:")
                            logger.info(f"  Address: '{geocoding_address}'")
                            logger.info(f"  City: '{geocoding_city}'")
                            logger.info(f"  County: '{geocoding_county}'")

                            # Geocode the cleaned address to get sector
                            logger.info("Calling Google Maps API...")
                            geocoding_result = await geocoding.geocode_address(
                                geocoding_address, geocoding_city, geocoding_county
                            )

                            logger.info("=== GOOGLE MAPS API RESPONSE ===")
                            logger.info(f"Raw geocoding result: {geocoding_result}")
                            logger.info(f"Result type: {type(geocoding_result)}")
                            logger.info(f"Result length: {len(geocoding_result) if geocoding_result else 'None'}")

                            if geocoding_result and len(geocoding_result) == 3:
                                new_address, new_city, new_county = geocoding_result
                                logger.info("=== GEOCODING RESULT PARSING ===")
                                logger.info(f"Parsed results:")
                                logger.info(f"  New Address: '{new_address}' (type: {type(new_address)})")
                                logger.info(f"  New City: '{new_city}' (type: {type(new_city)})")
                                logger.info(f"  New County: '{new_county}' (type: {type(new_county)})")

                                # Sanitize for logging
                                safe_new_address = (new_address or '').replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                                safe_new_city = (new_city or '').replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                                safe_new_county = (new_county or '').replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')

                                logger.info("=== SECTOR DETECTION ANALYSIS ===")
                                # Check if we got a sector
                                if new_city and "Sector" in new_city:
                                    logger.info(f"✓ SUCCESS: Sector detected in geocoding result!")
                                    logger.info(f"  Sector found: '{safe_new_city}'")
                                    logger.info(f"  Original city: '{current_city}' -> New city: '{safe_new_city}'")
                                else:
                                    logger.warning(f"✗ NO SECTOR FOUND in geocoding result")
                                    logger.info(f"  City returned: '{safe_new_city}'")
                                    logger.info(f"  Checking if 'Sector' keyword exists: {'Sector' in (new_city or '')}")
                                    logger.info("  Possible reasons:")
                                    logger.info("    - Address doesn't have enough detail for Google Maps")
                                    logger.info("    - Address is not in a numbered sector")
                                    logger.info("    - Google Maps returned general city name instead of sector")
                            else:
                                logger.warning(f"✗ INVALID GEOCODING RESULT")
                                logger.info(f"Expected: tuple with 3 elements")
                                logger.info(f"Received: {geocoding_result}")
                                new_address, new_city, new_county = None, None, None

                        except Exception as geocoding_error:
                            logger.error(f"✗ GEOCODING FAILED with exception: {geocoding_error}")
                            logger.error(f"Exception type: {type(geocoding_error).__name__}")
                            new_address, new_city, new_county = None, None, None

                        if new_city and "Sector" in new_city:
                            logger.info(f"=== SECTOR UPDATE PROCESS START ===")
                            logger.info(f"Found sector: {new_city}")
                            record.new_city = new_city
                            record.new_address = new_address or current_address
                            record.new_county = new_county or current_county

                            # Update the city field with the sector - MAKE IT VISIBLE
                            logger.info(f"=== UPDATING CITY FIELD ===")
                            logger.info(f"Changing city from '{current_city}' to '{new_city}'")

                            city_update_selectors = [
                                '#client_city',  # Based on your modal HTML
                                'input[name="city"]',
                                'input[id="city"]',
                                'input[name="oras"]',
                                'input[id="oras"]',
                                'select[name="city"]',
                                'select[id="city"]'
                            ]

                            logger.info("=== FIELD UPDATE VERIFICATION PROCESS ===")
                            city_updated = False

                            for i, selector in enumerate(city_update_selectors):
                                logger.info(f"=== FIELD UPDATE ATTEMPT {i+1}/{len(city_update_selectors)} ===")
                                logger.info(f"Trying selector: {selector}")

                                try:
                                    city_field = await edit_tab.query_selector(selector)
                                    if city_field:
                                        logger.info(f"  ✓ Field element found with selector: {selector}")

                                        # Check field properties
                                        is_visible = await city_field.is_visible()
                                        is_enabled = await city_field.is_enabled()
                                        is_editable = await city_field.is_editable()
                                        logger.info(f"  ✓ Field state - visible: {is_visible}, enabled: {is_enabled}, editable: {is_editable}")

                                        # Get current value before update
                                        if selector.startswith('select'):
                                            current_value = await city_field.evaluate('el => el.options[el.selectedIndex]?.text || el.value')
                                            logger.info(f"  ✓ Current SELECT value: '{current_value}'")
                                        else:
                                            current_value = await city_field.input_value()
                                            logger.info(f"  ✓ Current INPUT value: '{current_value}'")

                                        logger.info(f"  → UPDATING: '{current_value}' → '{new_city}'")

                                        # Scroll field into view and focus
                                        await city_field.scroll_into_view_if_needed()
                                        await city_field.focus()
                                        await asyncio.sleep(1)

                                        if selector.startswith('select'):
                                            logger.info(f"  → Processing SELECT element...")
                                            # For select, try to find option with sector name
                                            try:
                                                await city_field.select_option(label=new_city)
                                                logger.info(f"  ✓ Selected option by label: '{new_city}'")
                                            except Exception as e1:
                                                logger.info(f"  ⚠ Label selection failed: {e1}")
                                                try:
                                                    await city_field.select_option(value=new_city)
                                                    logger.info(f"  ✓ Selected option by value: '{new_city}'")
                                                except Exception as e2:
                                                    logger.warning(f"  ✗ Both label and value selection failed: {e2}")
                                                    continue
                                        else:
                                            logger.info(f"  → Processing INPUT element...")
                                            # For input, use visible typing instead of fill()
                                            logger.info(f"  → Clearing field and typing '{new_city}' visibly...")

                                            # Clear field first
                                            await city_field.click(click_count=3)  # Select all
                                            await asyncio.sleep(0.5)
                                            await city_field.press('Delete')
                                            await asyncio.sleep(0.5)

                                            # Type the new value character by character (visible)
                                            await city_field.type(new_city, delay=100)  # 100ms delay between characters
                                            logger.info(f"  ✓ Finished typing '{new_city}' into field")

                                        # === FIELD UPDATE VERIFICATION ===
                                        logger.info(f"  === VERIFYING FIELD UPDATE ===")
                                        await asyncio.sleep(2)

                                        if selector.startswith('select'):
                                            updated_value = await city_field.evaluate('el => el.options[el.selectedIndex]?.text || el.value')
                                            logger.info(f"  ✓ SELECT value after update: '{updated_value}'")
                                        else:
                                            updated_value = await city_field.input_value()
                                            logger.info(f"  ✓ INPUT value after update: '{updated_value}'")

                                        # Verify the change took effect
                                        if updated_value == new_city:
                                            logger.info(f"  ✓ SUCCESS: Field update verified! '{current_value}' → '{updated_value}'")
                                            city_updated = True
                                        elif updated_value == current_value:
                                            logger.warning(f"  ✗ FAILED: Field value unchanged! Still: '{updated_value}'")
                                        else:
                                            logger.warning(f"  ⚠ PARTIAL: Field changed but not to expected value!")
                                            logger.warning(f"    Expected: '{new_city}'")
                                            logger.warning(f"    Actual: '{updated_value}'")
                                            logger.warning(f"    Original: '{current_value}'")

                                        if city_updated:
                                            logger.info(f"  ✓ FIELD UPDATE SUCCESSFUL - breaking from loop")
                                            break
                                        else:
                                            logger.info(f"  → Trying next selector...")
                                    else:
                                        logger.info(f"  ✗ No element found with selector: {selector}")
                                except Exception as e:
                                    logger.warning(f"  ✗ Error with selector {selector}: {e}")
                                    continue

                            # Final verification summary
                            if city_updated:
                                logger.info(f"✓ CITY FIELD UPDATE COMPLETED SUCCESSFULLY")

                                # Take screenshot for debugging (optional)
                                try:
                                    screenshot_path = f"debug_city_update_{record.client_id}.png"
                                    await edit_tab.screenshot(path=screenshot_path)
                                    logger.info(f"Screenshot saved: {screenshot_path}")
                                except Exception:
                                    pass  # Screenshot is optional
                            else:
                                logger.warning(f"✗ CITY FIELD UPDATE FAILED - no selectors worked")

                            if city_updated:
                                # Save client details using the exact button from your HTML
                                logger.info("=== SAVE PROCESS START ===")
                                logger.info("Looking for save button...")
                                save_selectors = [
                                    '#addClientBtn',  # Based on your modal HTML
                                    'button:has-text("Salveaza date client")',
                                    'button:has-text("Salvare")',
                                    'button:has-text("Save")',
                                    'input[type="submit"]',
                                    'button[type="submit"]',
                                    '.save-btn',
                                    'button[onclick*="save"]',
                                    'button[onclick*="client"]'
                                ]

                                save_clicked = False
                                for selector in save_selectors:
                                    try:
                                        save_btn = await edit_tab.query_selector(selector)
                                        if save_btn:
                                            is_visible = await save_btn.is_visible()
                                            is_enabled = await save_btn.is_enabled()
                                            button_text = await save_btn.text_content()
                                            logger.info(f"Found save button with selector {selector}")
                                            logger.info(f"  Visible: {is_visible}, Enabled: {is_enabled}, Text: '{button_text}'")

                                            if is_visible and is_enabled:
                                                # Scroll to button and click
                                                await save_btn.scroll_into_view_if_needed()
                                                await asyncio.sleep(1)
                                                await save_btn.click()
                                                logger.info(f"Clicked save button: '{button_text}'")
                                                save_clicked = True

                                                # Wait for save operation and monitor modal
                                                logger.info("Waiting for save operation to complete...")
                                                await asyncio.sleep(2)

                                                # Check if modal is still visible (should close after save)
                                                modal_still_visible = False
                                                for modal_selector in ['.modal.show', '.modal.in', '.modal[style*="display: block"]']:
                                                    try:
                                                        modal = await edit_tab.query_selector(modal_selector)
                                                        if modal and await modal.is_visible():
                                                            modal_still_visible = True
                                                            break
                                                    except Exception:
                                                        continue

                                                if modal_still_visible:
                                                    logger.info("Modal still visible - save may be in progress...")
                                                    await asyncio.sleep(3)  # Wait longer
                                                else:
                                                    logger.info("Modal closed - save appears successful")

                                                break
                                    except Exception as e:
                                        logger.warning(f"Save button click failed for {selector}: {e}")
                                        continue

                                if save_clicked:
                                    logger.info(f"=== CLIENT SAVE COMPLETED ===")
                                    logger.info(f"Successfully updated client address for {record.client_id}")
                                    logger.info(f"Changed city from '{current_city}' to '{new_city}'")

                                    # === CRITICAL: CLOSE CLIENT MODAL AFTER SAVE ===
                                    logger.info("=== CLOSING CLIENT MODAL AFTER SAVE ===")
                                    try:
                                        # Wait for client save to complete
                                        await asyncio.sleep(2)

                                        # Close the client edit modal before saving invoice
                                        modal_close_selectors = [
                                            'button.close',
                                            '.modal-header .close',
                                            '[data-dismiss="modal"]',
                                            '.modal .btn-secondary:has-text("Renunta")',
                                            '.modal .btn:has-text("Close")',
                                            '.modal .btn:has-text("×")',
                                            '.modal .btn-secondary',
                                            'button[type="button"]:has-text("Renunta")'
                                        ]

                                        modal_closed = False
                                        for close_selector in modal_close_selectors:
                                            try:
                                                close_btn = await edit_tab.query_selector(close_selector)
                                                if close_btn and await close_btn.is_visible():
                                                    await close_btn.click()
                                                    logger.info(f"Successfully closed modal using selector: {close_selector}")
                                                    modal_closed = True
                                                    await asyncio.sleep(2)  # Wait for modal to close
                                                    break
                                            except Exception as close_e:
                                                logger.debug(f"Close selector {close_selector} failed: {close_e}")
                                                continue

                                        if modal_closed:
                                            logger.info("SUCCESS: Client modal closed successfully after save")
                                        else:
                                            logger.warning("⚠ Could not close client modal - trying escape key")
                                            try:
                                                await edit_tab.keyboard.press('Escape')
                                                await asyncio.sleep(1)
                                                logger.info("SUCCESS: Pressed Escape to close modal")
                                            except Exception:
                                                logger.warning("Could not press Escape key")

                                    except Exception as modal_close_e:
                                        logger.error(f"Error closing client modal after save: {modal_close_e}")

                                    # === INVOICE SAVE (AFTER MODAL IS CLOSED) ===
                                    logger.info("=== SAVING INVOICE (MODAL NOW CLOSED) ===")
                                    try:
                                        # Additional wait to ensure modal is fully closed
                                        await asyncio.sleep(1)

                                        # Look for the "Salveaza Factura" button
                                        invoice_save_selectors = [
                                            '#saveInvoiceBtn',  # The exact ID from your HTML
                                            'button:has-text("Salveaza Factura")',
                                            'button:has-text("Salvati Factura")',
                                            'button[title="Salvati Factura"]',
                                            'button.btn-primary:has-text("Salveaza")',
                                        ]

                                        invoice_saved = False
                                        for i, selector in enumerate(invoice_save_selectors):
                                            logger.info(f"Trying invoice save selector {i+1}/{len(invoice_save_selectors)}: {selector}")
                                            try:
                                                save_invoice_btn = await edit_tab.query_selector(selector)
                                                if save_invoice_btn:
                                                    is_visible = await save_invoice_btn.is_visible()
                                                    is_enabled = await save_invoice_btn.is_enabled()
                                                    button_text = await save_invoice_btn.text_content()
                                                    logger.info(f"  Found invoice save button - Visible: {is_visible}, Enabled: {is_enabled}")
                                                    logger.info(f"  Button text: '{button_text}'")

                                                    if is_visible and is_enabled:
                                                        # Scroll to button and click
                                                        await save_invoice_btn.scroll_into_view_if_needed()
                                                        await asyncio.sleep(1)
                                                        await save_invoice_btn.click()
                                                        logger.info(f"SUCCESS: Clicked invoice save button - '{button_text}'")

                                                        # Wait for save operation to complete
                                                        logger.info("Waiting for invoice save operation...")
                                                        await asyncio.sleep(3)

                                                        invoice_saved = True
                                                        break
                                                    else:
                                                        logger.info(f"  Button not clickable - Visible: {is_visible}, Enabled: {is_enabled}")
                                                else:
                                                    logger.info(f"  No button found with selector: {selector}")
                                            except Exception as e:
                                                logger.warning(f"  Error with selector {selector}: {e}")
                                                continue

                                        if invoice_saved:
                                            logger.info("SUCCESS: Invoice save completed successfully")
                                        else:
                                            logger.warning("WARNING: Could not find or click invoice save button")

                                    except Exception as e:
                                        logger.error(f"Error saving invoice immediately: {e}")
                                else:
                                    logger.warning("Could not find or click save button")
                                    # Try to close modal manually if save failed
                                    try:
                                        close_selectors = ['button.close', '.modal-header .close', '[data-dismiss="modal"]']
                                        for close_selector in close_selectors:
                                            close_btn = await edit_tab.query_selector(close_selector)
                                            if close_btn and await close_btn.is_visible():
                                                await close_btn.click()
                                                logger.info("Closed modal manually")
                                                break
                                    except Exception:
                                        pass
                            else:
                                logger.warning(f"Could not update city field - no suitable field found")
                        else:
                            logger.info(f"No sector found for address: {current_address}")
                else:
                    safe_city_log = current_city.replace('ș', 's').replace('ț', 't').replace('ă', 'a').replace('â', 'a').replace('î', 'i')
                logger.info(f"Not a Bucuresti city or no city found. City: '{safe_city_log}'")

                # === CRITICAL: CLOSE CLIENT MODAL BEFORE SAVING INVOICE ===
                logger.info("=== CLOSING CLIENT MODAL (NO UPDATES NEEDED) ===")
                try:
                    # Close the client edit modal since no updates are needed
                    modal_close_selectors = [
                        'button.close',
                        '.modal-header .close',
                        '[data-dismiss="modal"]',
                        '.modal .btn-secondary:has-text("Renunta")',
                        '.modal .btn:has-text("Close")',
                        '.modal .btn:has-text("×")',
                        '.modal .btn-secondary',
                        'button[type="button"]:has-text("Renunta")'
                    ]

                    modal_closed = False
                    for close_selector in modal_close_selectors:
                        try:
                            close_btn = await edit_tab.query_selector(close_selector)
                            if close_btn and await close_btn.is_visible():
                                await close_btn.click()
                                logger.info(f"Successfully closed modal using selector: {close_selector}")
                                modal_closed = True
                                await asyncio.sleep(2)  # Wait for modal to close
                                break
                        except Exception as close_e:
                            logger.debug(f"Close selector {close_selector} failed: {close_e}")
                            continue

                    if modal_closed:
                        logger.info("SUCCESS: Client modal closed successfully")
                    else:
                        logger.warning("⚠ Could not close client modal - trying escape key")
                        try:
                            await edit_tab.keyboard.press('Escape')
                            await asyncio.sleep(1)
                            logger.info("SUCCESS: Pressed Escape to close modal")
                        except Exception:
                            logger.warning("Could not press Escape key")

                except Exception as modal_close_e:
                    logger.error(f"Error closing client modal: {modal_close_e}")

                logger.info("=== MODAL INTERACTION END ===")

            except Exception as e:
                logger.error(f"Error processing client address: {e}")

            # === SAVE INVOICE STEP ===
            # After updating client details, we must save the invoice itself
            logger.info("=== SAVING INVOICE ===")
            try:
                # Look for the "Salveaza Factura" button
                invoice_save_selectors = [
                    '#saveInvoiceBtn',  # The exact ID from your HTML
                    'button:has-text("Salveaza Factura")',
                    'button:has-text("Salvati Factura")',
                    'button[title="Salvati Factura"]',
                    'button.btn-primary:has-text("Salveaza")',
                    'button[onclick*="save"]',
                    'button[type="button"]:has-text("Salveaza")'
                ]

                invoice_saved = False
                for i, selector in enumerate(invoice_save_selectors):
                    logger.info(f"Trying invoice save selector {i+1}/{len(invoice_save_selectors)}: {selector}")
                    try:
                        save_invoice_btn = await edit_tab.query_selector(selector)
                        if save_invoice_btn:
                            is_visible = await save_invoice_btn.is_visible()
                            is_enabled = await save_invoice_btn.is_enabled()
                            button_text = await save_invoice_btn.text_content()
                            logger.info(f"  Found invoice save button - Visible: {is_visible}, Enabled: {is_enabled}")
                            logger.info(f"  Button text: '{button_text}'")

                            if is_visible and is_enabled:
                                # Scroll to button and click
                                await save_invoice_btn.scroll_into_view_if_needed()
                                await asyncio.sleep(1)
                                await save_invoice_btn.click()
                                logger.info(f"SUCCESS: Clicked invoice save button - '{button_text}'")

                                # Wait for save operation to complete
                                logger.info("Waiting for invoice save operation...")
                                await asyncio.sleep(3)

                                # Check for any success indicators or page changes
                                try:
                                    # Look for success messages or page navigation
                                    current_url = edit_tab.url
                                    logger.info(f"Current URL after save: {current_url}")

                                    # Check for success messages
                                    success_indicators = [
                                        '.alert-success',
                                        '.success-message',
                                        '[class*="success"]',
                                        ':has-text("salvat")',
                                        ':has-text("success")'
                                    ]

                                    for indicator in success_indicators:
                                        try:
                                            success_element = await edit_tab.query_selector(indicator)
                                            if success_element and await success_element.is_visible():
                                                success_text = await success_element.text_content()
                                                logger.info(f"Success indicator found: '{success_text}'")
                                                break
                                        except Exception:
                                            continue

                                except Exception as e:
                                    logger.debug(f"Could not check success indicators: {e}")

                                invoice_saved = True
                                break
                            else:
                                logger.info(f"  Button not clickable - Visible: {is_visible}, Enabled: {is_enabled}")
                        else:
                            logger.info(f"  No button found with selector: {selector}")
                    except Exception as e:
                        logger.warning(f"  Error with selector {selector}: {e}")
                        continue

                if invoice_saved:
                    logger.info("SUCCESS: Invoice save completed successfully")
                else:
                    logger.warning("WARNING: Could not find or click invoice save button")
                    logger.warning("The client details were updated but the invoice may not be saved")

            except Exception as e:
                logger.error(f"Error saving invoice: {e}")

            # === COMPREHENSIVE CLEANUP AND STATE RESET ===
            logger.info("=== STARTING COMPREHENSIVE CLEANUP ===")

            # Step 1: Close any open modals in edit tab before closing tab
            try:
                if 'edit_tab' in locals() and edit_tab:
                    logger.info("Cleaning up edit tab state...")

                    # Force close any open modals
                    modal_close_selectors = [
                        'button.close',
                        '.modal-header .close',
                        '[data-dismiss="modal"]',
                        '.modal .btn-secondary:has-text("Renunta")',
                        '.modal .btn:has-text("Close")',
                        '.modal .btn:has-text("×")'
                    ]

                    for close_selector in modal_close_selectors:
                        try:
                            close_btn = await edit_tab.query_selector(close_selector)
                            if close_btn and await close_btn.is_visible():
                                await close_btn.click()
                                logger.info(f"Closed modal using selector: {close_selector}")
                                await asyncio.sleep(1)
                                break
                        except Exception:
                            continue

                    # Close the edit tab
                    await edit_tab.close()
                    logger.info("Successfully closed edit tab")

            except Exception as close_e:
                logger.debug(f"Error during edit tab cleanup: {close_e}")

            # Step 2: Get fresh main page reference and reset state
            try:
                logger.info("Getting fresh main page reference...")
                pages = self.context.pages
                main_page = None

                # Find the main invoices page
                for page in pages:
                    try:
                        page_url = page.url
                        if 'raport/facturi' in page_url:
                            main_page = page
                            logger.info(f"Found main invoices page: {page_url}")
                            break
                    except Exception:
                        continue

                if main_page:
                    await main_page.bring_to_front()
                    self.page = main_page
                    logger.info("Returned to main invoices tab with fresh page reference")

                    # Step 3: Reset page state - close any open dropdowns
                    logger.info("Resetting page state...")
                    try:
                        # Click elsewhere to close any open dropdowns
                        await main_page.click('body', timeout=5000)
                        await asyncio.sleep(1)

                        # Press Escape to close any open elements
                        await main_page.keyboard.press('Escape')
                        await asyncio.sleep(1)

                        logger.info("Page state reset completed")

                    except Exception as reset_e:
                        logger.debug(f"Page state reset error: {reset_e}")

                else:
                    # Fallback to first available page
                    if pages:
                        await pages[0].bring_to_front()
                        self.page = pages[0]
                        logger.info("Using first available page as fallback")
                    else:
                        logger.error("No pages available")

            except Exception as return_e:
                logger.warning(f"Could not return to main tab: {return_e}")
                # Try to recreate main tab reference if needed
                try:
                    pages = self.context.pages
                    if pages:
                        self.page = pages[0]  # Use first available page
                        logger.info("Using first available page as main tab after error")
                except Exception:
                    logger.error("Could not find any available pages")

            logger.info("=== COMPREHENSIVE CLEANUP COMPLETED ===")
            return True

        except Exception as e:
            logger.error(f"Failed to update invoice address for {record.client_id}: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Error details: {str(e)}")

            # Ensure we clean up the edit tab if it exists
            try:
                if 'edit_tab' in locals() and edit_tab:
                    await edit_tab.close()
                    logger.info("Cleaned up edit tab")
            except Exception as cleanup_e:
                logger.debug(f"Tab cleanup error: {cleanup_e}")

            # Ensure we return to main tab
            try:
                await main_tab.bring_to_front()
                self.page = main_tab
                logger.info("Returned to main tab after error")
            except Exception as return_e:
                logger.debug(f"Return to main tab error: {return_e}")
                # Try to find any available page
                try:
                    pages = self.context.pages
                    if pages:
                        self.page = pages[0]
                        logger.info("Using first available page after error")
                except Exception:
                    logger.error("No pages available after error")

            return False

    async def _verify_and_reset_page_state(self) -> bool:
        """
        Verify and reset page state to ensure clean processing of next invoice.
        This prevents infinite scroll loops and modal state issues.
        """
        try:
            logger.info("Verifying page state...")

            # Step 1: Ensure we're on the correct page
            current_url = self.page.url
            if 'raport/facturi' not in current_url:
                logger.error(f"Not on invoices page. Current URL: {current_url}")
                return False

            logger.info(f"✓ On correct page: {current_url}")

            # Step 2: Close any open modals
            logger.info("Closing any open modals...")
            modal_selectors = [
                '.modal.show',
                '.modal.in',
                '.modal[style*="display: block"]',
                '.modal-backdrop'
            ]

            modals_closed = 0
            for modal_selector in modal_selectors:
                try:
                    modals = await self.page.query_selector_all(modal_selector)
                    for modal in modals:
                        if await modal.is_visible():
                            # Try to close modal
                            close_btn = await modal.query_selector('button.close, .modal-header .close, [data-dismiss="modal"]')
                            if close_btn:
                                await close_btn.click()
                                modals_closed += 1
                                await asyncio.sleep(1)
                except Exception:
                    continue

            if modals_closed > 0:
                logger.info(f"✓ Closed {modals_closed} open modals")

            # Step 3: Close any open dropdowns
            logger.info("Closing any open dropdowns...")
            try:
                # Press Escape multiple times to close dropdowns
                await self.page.keyboard.press('Escape')
                await asyncio.sleep(0.5)
                await self.page.keyboard.press('Escape')
                await asyncio.sleep(0.5)

                # Click on page header to close dropdowns
                header = await self.page.query_selector('header, .page-header, .navbar')
                if header:
                    await header.click()
                    await asyncio.sleep(1)

                logger.info("✓ Dropdown cleanup completed")

            except Exception as e:
                logger.debug(f"Dropdown cleanup error: {e}")

            # Step 4: Verify table is visible and stable
            logger.info("Verifying invoices table state...")
            try:
                table = await self.page.query_selector('#invoices_datatable')
                if not table:
                    logger.error("Invoices table not found")
                    return False

                table_visible = await table.is_visible()
                if not table_visible:
                    logger.error("Invoices table not visible")
                    return False

                # Wait for table to be stable (no loading indicators)
                await asyncio.sleep(2)

                logger.info("✓ Invoices table is ready")

            except Exception as e:
                logger.error(f"Table verification failed: {e}")
                return False

            # Step 5: Reset scroll position to top
            logger.info("Resetting scroll position...")
            try:
                await self.page.evaluate('window.scrollTo(0, 0)')
                await asyncio.sleep(1)
                logger.info("✓ Scroll position reset")
            except Exception as e:
                logger.debug(f"Scroll reset error: {e}")

            logger.info("✅ Page state verification and reset completed successfully")
            return True

        except Exception as e:
            logger.error(f"Page state verification failed: {e}")
            return False
